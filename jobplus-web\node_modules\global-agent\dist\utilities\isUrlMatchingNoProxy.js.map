{"version": 3, "sources": ["../../src/utilities/isUrlMatchingNoProxy.js"], "names": ["subjectUrl", "noProxy", "subjectUrlTokens", "rules", "split", "rule", "ruleMatch", "replace", "match", "groups", "UnexpectedStateError", "hostname", "hostnameIsMatch", "matcher", "isMatch", "port"], "mappings": ";;;;;;;AAEA;;AAGA;;AACA;;;;8BAIgBA,U,EAAoBC,O,KAAoB;AACtD,QAAMC,gBAAgB,GAAG,gBAASF,UAAT,CAAzB;AAEA,QAAMG,KAAK,GAAGF,OAAO,CAACG,KAAR,CAAc,QAAd,CAAd;;AAEA,OAAK,MAAMC,IAAX,IAAmBF,KAAnB,EAA0B;AACxB,UAAMG,SAAS,GAAGD,IAAI,CACnBE,OADe,CACP,oBADO,EACe,GADf,EAEfC,KAFe,CAET,sCAFS,CAAlB;;AAIA,QAAI,CAACF,SAAD,IAAc,CAACA,SAAS,CAACG,MAA7B,EAAqC;AACnC,YAAM,IAAIC,4BAAJ,CAAyB,2BAAzB,CAAN;AACD;;AAED,QAAI,CAACJ,SAAS,CAACG,MAAV,CAAiBE,QAAtB,EAAgC;AAC9B,YAAM,IAAID,4BAAJ,CAAyB,4EAAzB,CAAN;AACD;;AAED,UAAME,eAAe,GAAGC,iBAAQC,OAAR,CAAgBZ,gBAAgB,CAACS,QAAjC,EAA2CL,SAAS,CAACG,MAAV,CAAiBE,QAA5D,CAAxB;;AAEA,QAAIC,eAAe,KAAK,CAACN,SAAS,CAACG,MAAX,IAAqB,CAACH,SAAS,CAACG,MAAV,CAAiBM,IAAvC,IAA+Cb,gBAAgB,CAACa,IAAjB,IAAyBb,gBAAgB,CAACa,IAAjB,KAA0BT,SAAS,CAACG,MAAV,CAAiBM,IAAxH,CAAnB,EAAkJ;AAChJ,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,C", "sourcesContent": ["// @flow\n\nimport {\n  parse as parseUrl,\n} from 'url';\nimport matcher from 'matcher';\nimport {\n  UnexpectedStateError,\n} from '../errors';\n\nexport default (subjectUrl: string, noProxy: string) => {\n  const subjectUrlTokens = parseUrl(subjectUrl);\n\n  const rules = noProxy.split(/[\\s,]+/);\n\n  for (const rule of rules) {\n    const ruleMatch = rule\n      .replace(/^(?<leadingDot>\\.)/, '*')\n      .match(/^(?<hostname>.+?)(?::(?<port>\\d+))?$/);\n\n    if (!ruleMatch || !ruleMatch.groups) {\n      throw new UnexpectedStateError('Invalid NO_PROXY pattern.');\n    }\n\n    if (!ruleMatch.groups.hostname) {\n      throw new UnexpectedStateError('NO_PROXY entry pattern must include hostname. Use * to match any hostname.');\n    }\n\n    const hostnameIsMatch = matcher.isMatch(subjectUrlTokens.hostname, ruleMatch.groups.hostname);\n\n    if (hostnameIsMatch && (!ruleMatch.groups || !ruleMatch.groups.port || subjectUrlTokens.port && subjectUrlTokens.port === ruleMatch.groups.port)) {\n      return true;\n    }\n  }\n\n  return false;\n};\n"], "file": "isUrlMatchingNoProxy.js"}