"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Brain,
  User,
  Clock,
  FileText,
  Video,
  Settings,
  LogOut,
  Plus,
  TrendingUp
} from "lucide-react";
import { PageTransition, FadeIn, StaggeredFadeIn } from "@/components/ui/page-transition";
import { useToastActions } from "@/components/ui/toast";
import { FullscreenControl } from "@/components/ui/fullscreen-control";

export default function DashboardPage() {
  const { user, signOut } = useAuth();
  const [stats, setStats] = useState({
    totalSessions: 0,
    totalDuration: 0,
    remainingCredits: 100,
    planType: 'free' as 'free' | 'premium'
  });

  const [recentSessions, setRecentSessions] = useState([
    {
      id: '1',
      company: '阿里巴巴',
      position: '前端工程师',
      date: '2024-08-01',
      duration: 1800, // 30分钟
      status: 'completed' as 'completed' | 'active'
    },
    {
      id: '2',
      company: '腾讯',
      position: '产品经理',
      date: '2024-07-28',
      duration: 2400, // 40分钟
      status: 'completed' as 'completed' | 'active'
    }
  ]);

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <ProtectedRoute>
      <PageTransition>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
          {/* Header */}
          <FadeIn direction="down">
            <header className="bg-white/80 backdrop-blur-sm border-b shadow-sm">
              <div className="container mx-auto px-4 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Brain className="h-8 w-8 text-blue-600" />
                  <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    JobPlus
                  </span>
                </div>
                <nav className="flex items-center space-x-4">
                  <Link href="/dashboard" className="text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50">
                    控制台
                  </Link>
                  <Link href="/resume" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                    简历中心
                  </Link>
                  <Link href="/interview" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                    面试室
                  </Link>
                  <Link href="/history" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                    面试记录
                  </Link>
                  <FullscreenControl />
                  <Button variant="ghost" size="icon" onClick={signOut} className="hover:bg-red-50 hover:text-red-600">
                    <LogOut className="h-4 w-4" />
                  </Button>
                </nav>
              </div>
            </header>
          </FadeIn>

          <div className="container mx-auto px-4 py-8">
          {/* Welcome Section */}
          <FadeIn delay={200}>
            <div className="mb-8 text-center">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">
                欢迎回来，{user?.user_metadata?.username || user?.email}！
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                准备好开始您的下一次面试了吗？让AI助手帮您在面试中脱颖而出。
              </p>
            </div>
          </FadeIn>

          {/* Stats Cards */}
          <FadeIn delay={400}>
            <div className="grid md:grid-cols-4 gap-6 mb-8">
            <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-white">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700">总面试次数</CardTitle>
                <Video className="h-5 w-5 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">{stats.totalSessions}</div>
                <p className="text-xs text-green-600 font-medium">
                  +2 较上月
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500 bg-gradient-to-r from-green-50 to-white">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-green-700">总使用时长</CardTitle>
                <Clock className="h-5 w-5 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">{formatDuration(stats.totalDuration)}</div>
                <p className="text-xs text-green-600 font-medium">
                  +1小时 较上月
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500 bg-gradient-to-r from-purple-50 to-white">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-purple-700">剩余积分</CardTitle>
                <TrendingUp className="h-5 w-5 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600">{stats.remainingCredits}</div>
                <p className="text-xs text-purple-600 font-medium">
                  {stats.planType === 'free' ? '免费版' : '高级版'}
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-white">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-orange-700">账户状态</CardTitle>
                <User className="h-5 w-5 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-600">活跃</div>
                <p className="text-xs text-orange-600 font-medium">
                  邮箱已验证
                </p>
              </CardContent>
            </Card>
          </div>
          </FadeIn>

          {/* Quick Actions */}
          <FadeIn delay={600}>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">快速操作</h2>
              <div className="grid md:grid-cols-3 gap-6">
                <Card className="group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                  <Link href="/interview">
                    <CardHeader className="text-center">
                      <div className="mx-auto w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <Video className="h-8 w-8 text-white" />
                      </div>
                      <CardTitle className="text-xl text-blue-700 group-hover:text-blue-800">
                        开始面试
                      </CardTitle>
                      <CardDescription className="text-blue-600">
                        进入AI面试助手，开始您的面试准备
                      </CardDescription>
                    </CardHeader>
                  </Link>
                </Card>

                <Card className="group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                  <Link href="/resume">
                    <CardHeader className="text-center">
                      <div className="mx-auto w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <FileText className="h-8 w-8 text-white" />
                      </div>
                      <CardTitle className="text-xl text-green-700 group-hover:text-green-800">
                        管理简历
                      </CardTitle>
                      <CardDescription className="text-green-600">
                        上传或编辑您的简历信息
                      </CardDescription>
                    </CardHeader>
                  </Link>
                </Card>

                <Card className="group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                  <Link href="/history">
                    <CardHeader className="text-center">
                      <div className="mx-auto w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <Clock className="h-8 w-8 text-white" />
                      </div>
                      <CardTitle className="text-xl text-purple-700 group-hover:text-purple-800">
                        查看记录
                      </CardTitle>
                      <CardDescription className="text-purple-600">
                        回顾您的面试历史和表现
                      </CardDescription>
                    </CardHeader>
                  </Link>
                </Card>
              </div>
            </div>
          </FadeIn>

          {/* Recent Sessions */}
          <FadeIn delay={800}>
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl text-gray-800">最近面试记录</CardTitle>
                  <Link href="/history">
                    <Button variant="outline" size="sm" className="hover:bg-blue-50 hover:border-blue-300">
                      查看全部
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                {recentSessions.length > 0 ? (
                  <div className="space-y-4">
                    {recentSessions.map((session, index) => (
                      <FadeIn key={session.id} delay={900 + index * 100}>
                        <div className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-all duration-200 hover:border-blue-200 bg-gradient-to-r from-white to-gray-50">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                              <Video className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-800">{session.company}</h4>
                              <p className="text-sm text-gray-600">{session.position}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-800">{formatDate(session.date)}</p>
                            <p className="text-sm text-blue-600 font-medium">{formatDuration(session.duration)}</p>
                          </div>
                        </div>
                      </FadeIn>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Video className="h-10 w-10 text-blue-500" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">还没有面试记录</h3>
                    <p className="text-gray-600 mb-6">开始您的第一次AI面试练习吧！</p>
                    <Link href="/interview">
                      <Button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg">
                        <Plus className="h-4 w-4 mr-2" />
                        开始第一次面试
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </FadeIn>
        </div>
        </div>
      </PageTransition>
    </ProtectedRoute>
  );
}
