"use client";

import { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Brain, 
  User, 
  Mail, 
  Lock, 
  Bell, 
  Shield,
  ArrowLeft,
  Save
} from "lucide-react";

export default function SettingsPage() {
  const { user, signOut } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState("");
  const [error, setError] = useState("");

  const [profile, setProfile] = useState({
    username: user?.user_metadata?.username || "",
    email: user?.email || "",
    phone: "",
    city: "",
  });

  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    interviewReminders: true,
    weeklyReports: false,
  });

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      // Here you would update the user profile in Supabase
      // const { error } = await supabase.auth.updateUser({
      //   data: { username: profile.username }
      // });
      
      setSuccess("个人信息更新成功！");
    } catch (err) {
      setError("更新失败，请稍后重试");
    } finally {
      setIsLoading(false);
    }
  };

  const handleNotificationUpdate = async () => {
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      // Here you would update notification preferences
      setSuccess("通知设置更新成功！");
    } catch (err) {
      setError("更新失败，请稍后重试");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white border-b">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <Brain className="h-8 w-8 text-blue-600" />
                <span className="text-2xl font-bold text-gray-900">JobPlus</span>
              </div>
            </div>
            <nav className="flex items-center space-x-4">
              <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">
                控制台
              </Link>
              <span className="text-blue-600 font-medium">设置</span>
            </nav>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">账户设置</h1>

            {success && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-800">{success}</p>
              </div>
            )}

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800">{error}</p>
              </div>
            )}

            <div className="grid gap-6">
              {/* Profile Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span>个人信息</span>
                  </CardTitle>
                  <CardDescription>
                    管理您的个人资料和联系信息
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleProfileUpdate} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="username">用户名</Label>
                        <Input
                          id="username"
                          value={profile.username}
                          onChange={(e) => setProfile(prev => ({ ...prev, username: e.target.value }))}
                          placeholder="请输入用户名"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">邮箱地址</Label>
                        <Input
                          id="email"
                          type="email"
                          value={profile.email}
                          disabled
                          className="bg-gray-50"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">手机号码</Label>
                        <Input
                          id="phone"
                          value={profile.phone}
                          onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                          placeholder="请输入手机号码"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="city">所在城市</Label>
                        <Input
                          id="city"
                          value={profile.city}
                          onChange={(e) => setProfile(prev => ({ ...prev, city: e.target.value }))}
                          placeholder="请输入所在城市"
                        />
                      </div>
                    </div>
                    <Button type="submit" disabled={isLoading}>
                      <Save className="h-4 w-4 mr-2" />
                      {isLoading ? "保存中..." : "保存更改"}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* Security Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>安全设置</span>
                  </CardTitle>
                  <CardDescription>
                    管理您的密码和安全选项
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">密码</h4>
                      <p className="text-sm text-gray-600">上次更新：30天前</p>
                    </div>
                    <Link href="/auth/change-password">
                      <Button variant="outline">
                        <Lock className="h-4 w-4 mr-2" />
                        修改密码
                      </Button>
                    </Link>
                  </div>
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">两步验证</h4>
                      <p className="text-sm text-gray-600">增强账户安全性</p>
                    </div>
                    <Button variant="outline" disabled>
                      即将推出
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Notification Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Bell className="h-5 w-5" />
                    <span>通知设置</span>
                  </CardTitle>
                  <CardDescription>
                    选择您希望接收的通知类型
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">邮件通知</h4>
                        <p className="text-sm text-gray-600">接收重要更新和提醒</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={notifications.emailNotifications}
                        onChange={(e) => setNotifications(prev => ({ 
                          ...prev, 
                          emailNotifications: e.target.checked 
                        }))}
                        className="h-4 w-4 text-blue-600 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">面试提醒</h4>
                        <p className="text-sm text-gray-600">面试前的提醒通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={notifications.interviewReminders}
                        onChange={(e) => setNotifications(prev => ({ 
                          ...prev, 
                          interviewReminders: e.target.checked 
                        }))}
                        className="h-4 w-4 text-blue-600 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">周报</h4>
                        <p className="text-sm text-gray-600">每周使用情况总结</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={notifications.weeklyReports}
                        onChange={(e) => setNotifications(prev => ({ 
                          ...prev, 
                          weeklyReports: e.target.checked 
                        }))}
                        className="h-4 w-4 text-blue-600 rounded"
                      />
                    </div>
                  </div>
                  <Button onClick={handleNotificationUpdate} disabled={isLoading}>
                    <Save className="h-4 w-4 mr-2" />
                    保存通知设置
                  </Button>
                </CardContent>
              </Card>

              {/* Danger Zone */}
              <Card className="border-red-200">
                <CardHeader>
                  <CardTitle className="text-red-600">危险操作</CardTitle>
                  <CardDescription>
                    这些操作不可逆，请谨慎操作
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="destructive" onClick={signOut}>
                    退出登录
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
