// AI Assistant for interview guidance and response generation

export interface InterviewContext {
  company: string;
  position: string;
  interviewType: 'technical' | 'behavioral' | 'mixed';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  industry?: string;
  userProfile?: {
    experience: string;
    skills: string[];
    education: string;
  };
}

export interface AIResponse {
  type: 'suggestion' | 'improvement' | 'encouragement' | 'warning';
  content: string;
  suggestions?: string[];
  confidence: number;
  category: 'technical' | 'communication' | 'behavioral' | 'general';
}

export interface QuestionAnalysis {
  questionType: 'technical' | 'behavioral' | 'situational' | 'general';
  difficulty: 'easy' | 'medium' | 'hard';
  keywords: string[];
  expectedAnswerStructure: string[];
  tips: string[];
}

export class AIAssistant {
  private context: InterviewContext;
  private conversationHistory: Array<{
    type: 'user' | 'interviewer' | 'ai';
    content: string;
    timestamp: Date;
  }> = [];

  constructor(context: InterviewContext) {
    this.context = context;
  }

  // Analyze user's spoken response and provide feedback
  async analyzeResponse(userResponse: string, detectedQuestion?: string): Promise<AIResponse> {
    // Add to conversation history
    this.conversationHistory.push({
      type: 'user',
      content: userResponse,
      timestamp: new Date(),
    });

    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1500));

    // Analyze response quality
    const analysis = this.analyzeResponseQuality(userResponse);
    
    // Generate contextual feedback
    const feedback = this.generateFeedback(userResponse, analysis, detectedQuestion);
    
    return feedback;
  }

  // Analyze detected interview question and provide guidance
  async analyzeQuestion(question: string): Promise<QuestionAnalysis> {
    await new Promise(resolve => setTimeout(resolve, 500));

    const questionType = this.classifyQuestion(question);
    const difficulty = this.assessQuestionDifficulty(question);
    const keywords = this.extractKeywords(question);
    
    return {
      questionType,
      difficulty,
      keywords,
      expectedAnswerStructure: this.getAnswerStructure(questionType),
      tips: this.getQuestionSpecificTips(questionType, question),
    };
  }

  // Generate proactive suggestions based on interview progress
  async generateProactiveSuggestion(): Promise<AIResponse | null> {
    const recentResponses = this.conversationHistory.slice(-3);
    
    if (recentResponses.length < 2) {
      return null;
    }

    // Analyze patterns in recent responses
    const patterns = this.analyzeResponsePatterns(recentResponses);
    
    if (patterns.needsImprovement) {
      return {
        type: 'suggestion',
        content: patterns.suggestion,
        confidence: 0.8,
        category: patterns.category,
        suggestions: patterns.actionItems,
      };
    }

    return null;
  }

  private analyzeResponseQuality(response: string): {
    length: 'too_short' | 'appropriate' | 'too_long';
    structure: 'poor' | 'good' | 'excellent';
    specificity: 'vague' | 'specific' | 'very_specific';
    confidence: 'low' | 'medium' | 'high';
  } {
    const wordCount = response.split(' ').length;
    
    return {
      length: wordCount < 20 ? 'too_short' : wordCount > 200 ? 'too_long' : 'appropriate',
      structure: this.assessStructure(response),
      specificity: this.assessSpecificity(response),
      confidence: this.assessConfidence(response),
    };
  }

  private assessStructure(response: string): 'poor' | 'good' | 'excellent' {
    const hasIntro = /^(我认为|我觉得|在我看来|根据我的经验)/.test(response);
    const hasExample = /(例如|比如|举个例子|具体来说)/.test(response);
    const hasConclusion = /(总的来说|综上所述|因此|所以)/.test(response);
    
    const structureScore = (hasIntro ? 1 : 0) + (hasExample ? 1 : 0) + (hasConclusion ? 1 : 0);
    
    if (structureScore >= 2) return 'excellent';
    if (structureScore === 1) return 'good';
    return 'poor';
  }

  private assessSpecificity(response: string): 'vague' | 'specific' | 'very_specific' {
    const specificityIndicators = [
      /\d+年/, // years of experience
      /\d+个月/, // months
      /\d+%/, // percentages
      /具体来说/, // specifically
      /项目中/, // in projects
      /公司/, // company names
      /技术栈/, // tech stack
    ];

    const specificityScore = specificityIndicators.reduce(
      (score, pattern) => score + (pattern.test(response) ? 1 : 0),
      0
    );

    if (specificityScore >= 3) return 'very_specific';
    if (specificityScore >= 1) return 'specific';
    return 'vague';
  }

  private assessConfidence(response: string): 'low' | 'medium' | 'high' {
    const confidenceIndicators = {
      high: [/我确信/, /我相信/, /我擅长/, /我能够/],
      low: [/我觉得可能/, /也许/, /我不太确定/, /可能/],
    };

    const highConfidence = confidenceIndicators.high.some(pattern => pattern.test(response));
    const lowConfidence = confidenceIndicators.low.some(pattern => pattern.test(response));

    if (highConfidence && !lowConfidence) return 'high';
    if (lowConfidence && !highConfidence) return 'low';
    return 'medium';
  }

  private generateFeedback(
    response: string,
    analysis: ReturnType<typeof this.analyzeResponseQuality>,
    question?: string
  ): AIResponse {
    const feedbackTemplates = {
      length: {
        too_short: "您的回答比较简短，建议提供更多细节和具体例子来支撑您的观点。",
        too_long: "回答很详细，但可以更加简洁明了，突出重点信息。",
        appropriate: "回答长度适中，很好地平衡了详细性和简洁性。",
      },
      structure: {
        poor: "建议使用更清晰的结构来组织回答，比如：观点-例子-总结。",
        good: "回答结构不错，可以进一步优化逻辑顺序。",
        excellent: "回答结构非常清晰，逻辑性强！",
      },
      specificity: {
        vague: "建议提供更具体的例子、数据或项目经验来支撑您的回答。",
        specific: "回答包含了具体信息，很好！可以再增加一些量化数据。",
        very_specific: "回答非常具体，包含了丰富的细节和实例，很棒！",
      },
      confidence: {
        low: "回答中可以表现得更自信一些，突出您的能力和成就。",
        medium: "回答展现了适度的自信，很好的平衡。",
        high: "回答展现了很强的自信，给人留下深刻印象！",
      },
    };

    // Generate main feedback
    let mainFeedback = "";
    let suggestions: string[] = [];
    let responseType: AIResponse['type'] = 'suggestion';

    // Prioritize feedback based on most critical issues
    if (analysis.length === 'too_short') {
      mainFeedback = feedbackTemplates.length.too_short;
      suggestions.push("添加具体例子", "提供更多细节", "说明影响和结果");
    } else if (analysis.structure === 'poor') {
      mainFeedback = feedbackTemplates.structure.poor;
      suggestions.push("使用STAR方法", "先说观点再举例", "最后总结要点");
    } else if (analysis.specificity === 'vague') {
      mainFeedback = feedbackTemplates.specificity.vague;
      suggestions.push("提供具体数据", "举实际项目例子", "说明技术细节");
    } else {
      // Positive feedback
      mainFeedback = "回答很不错！" + feedbackTemplates.structure[analysis.structure];
      responseType = 'encouragement';
      suggestions.push("保持这种回答风格", "继续展现专业能力", "适当补充相关经验");
    }

    // Add context-specific suggestions
    if (this.context.interviewType === 'technical') {
      suggestions.push("展示技术深度", "说明解决方案", "提及最佳实践");
    } else if (this.context.interviewType === 'behavioral') {
      suggestions.push("使用STAR方法", "强调团队合作", "展示学习能力");
    }

    return {
      type: responseType,
      content: mainFeedback,
      suggestions: suggestions.slice(0, 4), // Limit to 4 suggestions
      confidence: 0.85,
      category: this.determineCategory(question || response),
    };
  }

  private classifyQuestion(question: string): QuestionAnalysis['questionType'] {
    const technicalKeywords = ['技术', '代码', '算法', '架构', '框架', '数据库', '性能', '优化'];
    const behavioralKeywords = ['团队', '冲突', '挑战', '失败', '成功', '领导', '合作', '沟通'];
    const situationalKeywords = ['如果', '假设', '遇到', '处理', '解决', '应对'];

    if (technicalKeywords.some(keyword => question.includes(keyword))) {
      return 'technical';
    }
    if (behavioralKeywords.some(keyword => question.includes(keyword))) {
      return 'behavioral';
    }
    if (situationalKeywords.some(keyword => question.includes(keyword))) {
      return 'situational';
    }
    return 'general';
  }

  private assessQuestionDifficulty(question: string): QuestionAnalysis['difficulty'] {
    const hardIndicators = ['架构', '设计模式', '性能优化', '分布式', '高并发'];
    const easyIndicators = ['介绍', '了解', '基础', '简单'];

    if (hardIndicators.some(indicator => question.includes(indicator))) {
      return 'hard';
    }
    if (easyIndicators.some(indicator => question.includes(indicator))) {
      return 'easy';
    }
    return 'medium';
  }

  private extractKeywords(question: string): string[] {
    // Simple keyword extraction (in a real implementation, use NLP)
    const commonWords = ['的', '是', '在', '有', '和', '与', '或', '但', '如果', '那么', '什么', '怎么', '为什么'];
    const words = question.split(/\s+|[，。！？；：]/).filter(word => 
      word.length > 1 && !commonWords.includes(word)
    );
    return words.slice(0, 5); // Return top 5 keywords
  }

  private getAnswerStructure(questionType: QuestionAnalysis['questionType']): string[] {
    const structures = {
      technical: ['理解问题', '说明方案', '举例说明', '总结优势'],
      behavioral: ['情况描述', '任务说明', '行动过程', '结果总结'],
      situational: ['分析情况', '制定策略', '执行步骤', '预期结果'],
      general: ['观点表达', '支撑论据', '具体例子', '简要总结'],
    };
    return structures[questionType];
  }

  private getQuestionSpecificTips(questionType: QuestionAnalysis['questionType'], question: string): string[] {
    const baseTips = {
      technical: [
        '展示技术深度和广度',
        '提供具体的代码示例或架构图',
        '说明技术选择的原因',
        '讨论性能和可维护性'
      ],
      behavioral: [
        '使用STAR方法组织回答',
        '强调个人贡献和成长',
        '展示团队合作能力',
        '体现解决问题的思路'
      ],
      situational: [
        '展示分析问题的能力',
        '提出多种解决方案',
        '考虑风险和应对措施',
        '体现决策思维过程'
      ],
      general: [
        '保持回答的逻辑性',
        '提供具体的例子',
        '展现积极的态度',
        '与职位要求相关联'
      ]
    };

    return baseTips[questionType] || baseTips.general;
  }

  private analyzeResponsePatterns(responses: Array<{ content: string }>): {
    needsImprovement: boolean;
    suggestion: string;
    category: AIResponse['category'];
    actionItems: string[];
  } {
    // Analyze for common patterns that need improvement
    const allResponses = responses.map(r => r.content).join(' ');
    
    // Check for repetitive language
    const repetitiveWords = ['我觉得', '我认为', '可能', '应该'];
    const repetitionCount = repetitiveWords.reduce(
      (count, word) => count + (allResponses.split(word).length - 1),
      0
    );

    if (repetitionCount > 5) {
      return {
        needsImprovement: true,
        suggestion: '注意避免重复使用相同的表达方式，尝试使用更多样化的语言来表达观点。',
        category: 'communication',
        actionItems: ['使用同义词替换', '变换句式结构', '增加表达多样性']
      };
    }

    // Check for lack of specificity
    const specificityScore = responses.reduce((score, response) => {
      return score + (this.assessSpecificity(response.content) === 'vague' ? 0 : 1);
    }, 0);

    if (specificityScore < responses.length / 2) {
      return {
        needsImprovement: true,
        suggestion: '建议在回答中增加更多具体的例子、数据和项目经验，让回答更有说服力。',
        category: 'general',
        actionItems: ['提供具体数据', '举实际例子', '说明具体成果']
      };
    }

    return {
      needsImprovement: false,
      suggestion: '',
      category: 'general',
      actionItems: []
    };
  }

  private determineCategory(text: string): AIResponse['category'] {
    if (/技术|代码|架构|算法/.test(text)) return 'technical';
    if (/团队|沟通|合作|领导/.test(text)) return 'communication';
    if (/经验|挑战|成长|学习/.test(text)) return 'behavioral';
    return 'general';
  }

  // Get conversation summary for session end
  getConversationSummary(): {
    totalResponses: number;
    averageResponseLength: number;
    strongPoints: string[];
    improvementAreas: string[];
    overallScore: number;
  } {
    const userResponses = this.conversationHistory.filter(msg => msg.type === 'user');
    
    const totalResponses = userResponses.length;
    const averageResponseLength = userResponses.reduce(
      (sum, response) => sum + response.content.split(' ').length,
      0
    ) / totalResponses;

    // Analyze overall performance
    const analyses = userResponses.map(response => 
      this.analyzeResponseQuality(response.content)
    );

    const strongPoints: string[] = [];
    const improvementAreas: string[] = [];

    // Determine strong points and areas for improvement
    const structureScores = analyses.map(a => a.structure);
    const specificityScores = analyses.map(a => a.specificity);
    const confidenceScores = analyses.map(a => a.confidence);

    if (structureScores.filter(s => s === 'excellent').length > totalResponses / 2) {
      strongPoints.push('回答结构清晰');
    }
    if (specificityScores.filter(s => s === 'very_specific').length > totalResponses / 2) {
      strongPoints.push('提供具体例子');
    }
    if (confidenceScores.filter(s => s === 'high').length > totalResponses / 2) {
      strongPoints.push('表现自信');
    }

    if (structureScores.filter(s => s === 'poor').length > totalResponses / 3) {
      improvementAreas.push('改善回答结构');
    }
    if (specificityScores.filter(s => s === 'vague').length > totalResponses / 3) {
      improvementAreas.push('增加具体细节');
    }

    // Calculate overall score (0-100)
    const scoreWeights = {
      structure: { poor: 0, good: 0.7, excellent: 1 },
      specificity: { vague: 0, specific: 0.7, very_specific: 1 },
      confidence: { low: 0, medium: 0.6, high: 1 }
    };

    const overallScore = Math.round(
      analyses.reduce((sum, analysis) => {
        return sum + 
          (scoreWeights.structure[analysis.structure] +
           scoreWeights.specificity[analysis.specificity] +
           scoreWeights.confidence[analysis.confidence]) / 3;
      }, 0) / totalResponses * 100
    );

    return {
      totalResponses,
      averageResponseLength: Math.round(averageResponseLength),
      strongPoints,
      improvementAreas,
      overallScore
    };
  }
}
