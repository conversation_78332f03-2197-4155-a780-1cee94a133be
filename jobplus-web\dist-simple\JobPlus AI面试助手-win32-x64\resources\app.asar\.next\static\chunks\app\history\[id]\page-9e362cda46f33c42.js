(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[369],{2175:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>E});var t=a(5155),c=a(2115),l=a(5695),r=a(6874),n=a.n(r),i=a(283),x=a(9053),m=a(285),d=a(6695),h=a(9148),o=a(1007),j=a(5657),u=a(1497),p=a(4357),N=a(8564),g=a(5169),y=a(9376),f=a(1788),v=a(9037),b=a(646),w=a(6785),A=a(463),k=a(3717),S=a(4229),C=a(4416),R=a(8136),Z=a(7576),T=a(9074),_=a(4186),z=a(688),B=a(6982);function E(){let e,s=(0,l.useParams)(),a=(0,l.useRouter)(),{user:r}=(0,i.A)();(0,B.EM)();let[E,$]=(0,c.useState)(null),[I,L]=(0,c.useState)(!0),[O,P]=(0,c.useState)(!1),[D,U]=(0,c.useState)(""),[W,M]=(0,c.useState)(0),J=new h.E;(0,c.useEffect)(()=>{F()},[s.id]);let F=()=>{L(!0);try{let e=J.getSessionById(s.id);e?($(e),U(e.userNotes||""),M(e.rating||0)):a.push("/history")}catch(e){console.error("Failed to load session:",e),a.push("/history")}finally{L(!1)}},q=e=>{let s=Math.floor(e/3600),a=Math.floor(e%3600/60);return s>0?"".concat(s,"小时").concat(a,"分钟"):"".concat(a,"分钟")},G=e=>e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return I?(0,t.jsx)(x.A,{children:(0,t.jsx)(z.Z_,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"加载面试详情中..."})]})})})}):E?(0,t.jsx)(x.A,{children:(0,t.jsx)(z.Z_,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,t.jsx)("header",{className:"bg-white border-b",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n(),{href:"/history",children:(0,t.jsx)(m.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"JobPlus"})]})]}),(0,t.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900",children:"控制台"}),(0,t.jsx)(n(),{href:"/history",className:"text-gray-600 hover:text-gray-900",children:"面试记录"}),(0,t.jsx)("span",{className:"text-blue-600 font-medium",children:"详情"})]})]})}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:[E.company," - ",E.position]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-gray-600",children:[(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"active":return"text-blue-600 bg-blue-100";case"paused":return"text-yellow-600 bg-yellow-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(E.status)),children:(e=>{switch(e){case"completed":return"已完成";case"active":return"进行中";case"paused":return"已暂停";case"cancelled":return"已取消";default:return"未知"}})(E.status)}),(0,t.jsx)("span",{children:(e=>{switch(e){case"technical":return"技术面试";case"behavioral":return"行为面试";case"mixed":return"综合面试";default:return e}})(E.interviewType)}),(0,t.jsx)("span",{children:G(E.startTime)}),(0,t.jsx)("span",{children:q(E.duration)})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(m.$,{onClick:()=>{if(E){let e=new Blob([JSON.stringify(E,null,2)],{type:"application/json"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="interview-".concat(E.company,"-").concat(E.position,"-").concat(E.startTime.toISOString().split("T")[0],".json"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)}},variant:"outline",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"导出"]})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"评分："}),(e=e=>{E&&J.updateSessionRating(E.id,e)&&($({...E,rating:e}),M(e))},(0,t.jsx)("div",{className:"flex items-center space-x-1",children:[1,2,3,4,5].map(s=>(0,t.jsx)("button",{onClick:()=>e(s),className:"p-1 rounded ".concat(s<=W?"text-yellow-500":"text-gray-300"," hover:text-yellow-400 transition-colors"),children:(0,t.jsx)(N.A,{className:"h-5 w-5 fill-current"})},s))})),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:W>0?"".concat(W,"/5 星"):"未评分"})]})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[E.summary&&(0,t.jsxs)(d.Zp,{children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)(d.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"面试总结"})]})}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:E.summary.overallScore}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"综合评分"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-1",children:E.summary.totalResponses}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"回答次数"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[E.summary.strongPoints.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-900 mb-2 flex items-center",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"表现优势"]}),(0,t.jsx)("ul",{className:"text-sm text-green-800 space-y-1",children:E.summary.strongPoints.map((e,s)=>(0,t.jsxs)("li",{children:["• ",e]},s))})]}),E.summary.improvementAreas.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-orange-900 mb-2 flex items-center",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"改进建议"]}),(0,t.jsx)("ul",{className:"text-sm text-orange-800 space-y-1",children:E.summary.improvementAreas.map((e,s)=>(0,t.jsxs)("li",{children:["• ",e]},s))})]}),E.summary.keyInsights&&E.summary.keyInsights.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-blue-900 mb-2 flex items-center",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"关键洞察"]}),(0,t.jsx)("ul",{className:"text-sm text-blue-800 space-y-1",children:E.summary.keyInsights.map((e,s)=>(0,t.jsxs)("li",{children:["• ",e]},s))})]})]})]})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"对话记录"})]}),(0,t.jsx)(d.BT,{children:"完整的面试对话历史，包括您的回答和AI建议"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===E.messages.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-8",children:"暂无对话记录"}):E.messages.map((e,s)=>(0,t.jsx)("div",{className:"flex ".concat("user"===e.type?"justify-end":"justify-start"," mb-4"),children:(0,t.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-3 rounded-lg ".concat("user"===e.type?"bg-blue-600 text-white":"ai"===e.type?"bg-green-100 text-green-900":"bg-gray-100 text-gray-900"),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:["user"===e.type?(0,t.jsx)(o.A,{className:"h-4 w-4"}):"ai"===e.type?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-xs opacity-75",children:e.timestamp.toLocaleTimeString()}),(0,t.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>{var s;return s=e.content,void navigator.clipboard.writeText(s).then(()=>{alert("已复制到剪贴板")})},className:"h-6 w-6 p-0 opacity-50 hover:opacity-100",children:(0,t.jsx)(p.A,{className:"h-3 w-3"})})]}),(0,t.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}),e.suggestions&&e.suggestions.length>0&&(0,t.jsx)("div",{className:"mt-2 space-y-1",children:e.suggestions.map((e,s)=>(0,t.jsxs)("div",{className:"text-xs bg-white bg-opacity-20 px-2 py-1 rounded",children:["\uD83D\uDCA1 ",e]},s))})]})},e.id))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(d.Zp,{children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)(d.ZB,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"个人备注"})]}),!O&&(0,t.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>P(!0),children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(d.Wu,{children:O?(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("textarea",{value:D,onChange:e=>U(e.target.value),placeholder:"添加您的备注...",className:"w-full p-3 border rounded-md resize-none",rows:4}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(m.$,{onClick:()=>{E&&(J.updateSessionNotes(E.id,D)?($({...E,userNotes:D}),P(!1)):alert("保存失败，请重试"))},size:"sm",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"保存"]}),(0,t.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>{P(!1),U(E.userNotes||"")},children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"取消"]})]})]}):(0,t.jsx)("div",{children:D?(0,t.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:D}):(0,t.jsx)("p",{className:"text-sm text-gray-500 italic",children:"暂无备注"})})})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsx)(d.aR,{children:(0,t.jsx)(d.ZB,{children:"面试信息"})}),(0,t.jsxs)(d.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"font-medium",children:"公司："}),(0,t.jsx)("span",{children:E.company})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"font-medium",children:"职位："}),(0,t.jsx)("span",{children:E.position})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(T.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"font-medium",children:"开始时间："}),(0,t.jsx)("span",{children:G(E.startTime)})]}),E.endTime&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(T.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"font-medium",children:"结束时间："}),(0,t.jsx)("span",{children:G(E.endTime)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"font-medium",children:"时长："}),(0,t.jsx)("span",{children:q(E.duration)})]})]})]})]})]})]})]})})}):(0,t.jsx)(x.A,{children:(0,t.jsx)(z.Z_,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"面试记录不存在"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"请检查链接是否正确"}),(0,t.jsx)(n(),{href:"/history",children:(0,t.jsx)(m.$,{children:"返回面试记录"})})]})})})})}},4862:(e,s,a)=>{Promise.resolve().then(a.bind(a,2175))}},e=>{e.O(0,[598,874,289,642,441,964,358],()=>e(e.s=4862)),_N_E=e.O()}]);