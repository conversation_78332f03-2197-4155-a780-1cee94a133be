(()=>{var a={};a.id=736,a.ids=[736],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19958:(a,b,c)=>{Promise.resolve().then(c.bind(c,44049))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44049:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>E});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(63213),i=c(20769),j=c(29523),k=c(89667),l=c(88800),m=c(44493),n=c(78200),o=c(63143),p=c(40083),q=c(10022),r=c(62688);let s=(0,r.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),t=(0,r.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var u=c(8819),v=c(58869);let w=(0,r.A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var x=c(96474),y=c(88233),z=c(57800);let A=(0,r.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]);var B=c(41008),C=c(62694),D=c(94934);function E(){let{user:a}=(0,h.A)();(0,C.EM)();let b=(0,e.useRef)(null),[c,f]=(0,e.useState)(!1),[r,E]=(0,e.useState)(!1),[F,G]=(0,e.useState)(!1),[H,I]=(0,e.useState)(!1),[J,K]=(0,e.useState)({name:"",phone:"",email:a?.email||"",city:""}),[L,M]=(0,e.useState)([]),[N,O]=(0,e.useState)([]),[P,Q]=(0,e.useState)([]),[R,S]=(0,e.useState)({programming_languages:[],frameworks:[],databases:[],tools:[]}),T=async b=>{if(b.target.files?.[0]){f(!0),E(!0);try{await new Promise(a=>setTimeout(a,3e3)),K({name:"张三",phone:"13800138000",email:a?.email||"",city:"北京"}),M([{id:"1",school:"清华大学",degree:"本科",major:"计算机科学与技术",start_date:"2018-09",end_date:"2022-06"}]),O([{id:"1",company:"阿里巴巴",position:"前端工程师",start_date:"2022-07",end_date:"2024-08",responsibilities:["负责电商平台前端开发","使用React和TypeScript构建用户界面","优化页面性能，提升用户体验"]}]),Q([{id:"1",name:"电商管理系统",role:"前端负责人",description:"基于React的电商后台管理系统，支持商品管理、订单处理等功能",technologies:["React","TypeScript","Ant Design","Redux"]}]),S({programming_languages:["JavaScript","TypeScript","Python"],frameworks:["React","Vue.js","Node.js"],databases:["MySQL","MongoDB","Redis"],tools:["Git","Docker","Webpack"]}),I(!0),G(!0)}catch(a){console.error("Upload failed:",a)}finally{f(!1),E(!1)}}},U=(a,b,c)=>{M(L.map(d=>d.id===a?{...d,[b]:c}:d))},V=(a,b,c)=>{O(N.map(d=>d.id===a?{...d,[b]:c}:d))},W=async()=>{try{console.log("Saving resume...",{personalInfo:J,education:L,workExperience:N,projects:P,skills:R}),G(!1)}catch(a){console.error("Save failed:",a)}};return(0,d.jsx)(i.A,{children:(0,d.jsx)(B.Z_,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,d.jsx)(B._A,{direction:"down",children:(0,d.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(n.A,{className:"h-8 w-8 text-blue-600"}),(0,d.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,d.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,d.jsx)(g(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"控制台"}),(0,d.jsx)(g(),{href:"/resume",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"简历中心"}),(0,d.jsx)(g(),{href:"/interview",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试室"}),(0,d.jsx)(g(),{href:"/history",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试记录"}),H&&(0,d.jsxs)(j.$,{onClick:()=>G(!F),variant:F?"default":"outline",className:"ml-2",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),F?"预览模式":"编辑模式"]}),(0,d.jsx)(j.$,{variant:"ghost",size:"icon",onClick:()=>D.f.signOut(),className:"hover:bg-red-50 hover:text-red-600",children:(0,d.jsx)(p.A,{className:"h-4 w-4"})})]})]})})}),(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsx)(B._A,{delay:200,children:(0,d.jsxs)("div",{className:"mb-8 text-center",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:"简历管理中心"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"上传并管理您的简历，AI将帮助您优化内容并提取关键信息"})]})}),H?(0,d.jsxs)("div",{className:"space-y-6",children:[F&&(0,d.jsx)(B._A,{delay:400,children:(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsxs)(j.$,{onClick:W,className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"保存更改"]})})}),(0,d.jsx)(B._A,{delay:500,children:(0,d.jsxs)(m.Zp,{className:"shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,d.jsx)(m.aR,{className:"bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:(0,d.jsxs)(m.ZB,{className:"flex items-center space-x-2 text-xl text-gray-800",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,d.jsx)(v.A,{className:"h-5 w-5 text-white"})}),(0,d.jsx)("span",{children:"个人信息"})]})}),(0,d.jsx)(m.Wu,{children:(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"name",children:"姓名"}),(0,d.jsx)(k.p,{id:"name",value:J.name,onChange:a=>K(b=>({...b,name:a.target.value})),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"phone",children:"手机号码"}),(0,d.jsx)(k.p,{id:"phone",value:J.phone,onChange:a=>K(b=>({...b,phone:a.target.value})),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"email",children:"邮箱地址"}),(0,d.jsx)(k.p,{id:"email",value:J.email,onChange:a=>K(b=>({...b,email:a.target.value})),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"city",children:"所在城市"}),(0,d.jsx)(k.p,{id:"city",value:J.city,onChange:a=>K(b=>({...b,city:a.target.value})),disabled:!F})]})]})})]})}),(0,d.jsxs)(m.Zp,{children:[(0,d.jsx)(m.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(w,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"教育经历"})]}),F&&(0,d.jsxs)(j.$,{onClick:()=>{M([...L,{id:Date.now().toString(),school:"",degree:"",major:"",start_date:"",end_date:""}])},size:"sm",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"添加"]})]})}),(0,d.jsx)(m.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:L.map(a=>(0,d.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"学校"}),(0,d.jsx)(k.p,{value:a.school,onChange:b=>U(a.id,"school",b.target.value),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"学历"}),(0,d.jsx)(k.p,{value:a.degree,onChange:b=>U(a.id,"degree",b.target.value),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"专业"}),(0,d.jsx)(k.p,{value:a.major,onChange:b=>U(a.id,"major",b.target.value),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"时间"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(k.p,{type:"month",value:a.start_date,onChange:b=>U(a.id,"start_date",b.target.value),disabled:!F}),(0,d.jsx)(k.p,{type:"month",value:a.end_date,onChange:b=>U(a.id,"end_date",b.target.value),disabled:!F})]})]})]}),F&&(0,d.jsx)("div",{className:"mt-4 flex justify-end",children:(0,d.jsx)(j.$,{variant:"destructive",size:"sm",onClick:()=>{var b;return b=a.id,void M(L.filter(a=>a.id!==b))},children:(0,d.jsx)(y.A,{className:"h-4 w-4"})})})]},a.id))})})]}),(0,d.jsxs)(m.Zp,{children:[(0,d.jsx)(m.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(z.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"工作经历"})]}),F&&(0,d.jsxs)(j.$,{onClick:()=>{O([...N,{id:Date.now().toString(),company:"",position:"",start_date:"",end_date:"",responsibilities:[""]}])},size:"sm",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"添加"]})]})}),(0,d.jsx)(m.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:N.map(a=>(0,d.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"公司名称"}),(0,d.jsx)(k.p,{value:a.company,onChange:b=>V(a.id,"company",b.target.value),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"职位"}),(0,d.jsx)(k.p,{value:a.position,onChange:b=>V(a.id,"position",b.target.value),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"开始时间"}),(0,d.jsx)(k.p,{type:"month",value:a.start_date,onChange:b=>V(a.id,"start_date",b.target.value),disabled:!F})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"结束时间"}),(0,d.jsx)(k.p,{type:"month",value:a.end_date,onChange:b=>V(a.id,"end_date",b.target.value),disabled:!F})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"工作职责"}),(0,d.jsxs)("div",{className:"space-y-2",children:[a.responsibilities.map((b,c)=>(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(k.p,{value:b,onChange:b=>{let d=[...a.responsibilities];d[c]=b.target.value,V(a.id,"responsibilities",d)},disabled:!F,placeholder:"描述您的工作职责"}),F&&(0,d.jsx)(j.$,{variant:"outline",size:"icon",onClick:()=>{let b=a.responsibilities.filter((a,b)=>b!==c);V(a.id,"responsibilities",b)},children:(0,d.jsx)(y.A,{className:"h-4 w-4"})})]},c)),F&&(0,d.jsxs)(j.$,{variant:"outline",size:"sm",onClick:()=>{let b=[...a.responsibilities,""];V(a.id,"responsibilities",b)},children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"添加职责"]})]})]}),F&&(0,d.jsx)("div",{className:"mt-4 flex justify-end",children:(0,d.jsx)(j.$,{variant:"destructive",size:"sm",onClick:()=>{var b;return b=a.id,void O(N.filter(a=>a.id!==b))},children:(0,d.jsx)(y.A,{className:"h-4 w-4"})})})]},a.id))})})]}),(0,d.jsxs)(m.Zp,{children:[(0,d.jsx)(m.aR,{children:(0,d.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"技能专长"})]})}),(0,d.jsx)(m.Wu,{children:(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"编程语言"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:R.programming_languages.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:a},b))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"框架技术"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:R.frameworks.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm",children:a},b))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"数据库"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:R.databases.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm",children:a},b))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{children:"开发工具"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:R.tools.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm",children:a},b))})]})]})})]})]}):(0,d.jsx)(B._A,{delay:400,children:(0,d.jsxs)(m.Zp,{className:"text-center shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,d.jsxs)(m.aR,{className:"bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:[(0,d.jsxs)(m.ZB,{className:"flex items-center justify-center space-x-2 text-xl text-gray-800",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,d.jsx)(q.A,{className:"h-6 w-6 text-white"})}),(0,d.jsx)("span",{children:"上传您的简历"})]}),(0,d.jsx)(m.BT,{className:"text-gray-600",children:"支持PDF、Word格式，AI将自动解析并提取关键信息"})]}),(0,d.jsx)(m.Wu,{className:"space-y-6 p-8",children:r?(0,d.jsxs)("div",{className:"py-12",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(s,{className:"h-10 w-10 animate-spin text-blue-600"})}),(0,d.jsx)("p",{className:"text-lg font-medium text-gray-800 mb-2",children:"AI正在解析您的简历..."}),(0,d.jsx)("p",{className:"text-gray-600",children:"请稍候，这可能需要几秒钟"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"border-2 border-dashed border-blue-200 rounded-lg p-12 hover:border-blue-400 transition-all duration-300 cursor-pointer bg-gradient-to-br from-blue-50/50 to-white hover:shadow-lg group",onClick:()=>b.current?.click(),children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg",children:(0,d.jsx)(t,{className:"h-8 w-8 text-white"})}),(0,d.jsx)("p",{className:"text-lg font-medium text-gray-800 mb-2",children:"点击上传简历文件"}),(0,d.jsx)("p",{className:"text-gray-600",children:"或拖拽文件到此处"})]}),(0,d.jsx)("input",{ref:b,type:"file",accept:".pdf,.doc,.docx",onChange:T,className:"hidden"}),(0,d.jsx)("div",{className:"text-sm text-gray-500 text-center bg-gray-50 rounded-lg p-3",children:"支持格式：PDF, DOC, DOCX（最大10MB）"})]})})]})})]})})]})})})}},48574:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["resume",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,52616)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\resume\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\resume\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/resume/page",pathname:"/resume",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/resume/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},52616:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\jobplus\\\\1-前端服务\\\\ai-jobplus-web6\\\\jobplus-web\\\\src\\\\app\\\\resume\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\resume\\page.tsx","default")},56910:(a,b,c)=>{Promise.resolve().then(c.bind(c,52616))},57800:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58869:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},88800:(a,b,c)=>{"use strict";c.d(b,{J:()=>l});var d=c(60687),e=c(43210);c(51215);var f=c(81391),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),g=e.forwardRef((a,e)=>{let{asChild:f,...g}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f?c:b,{...g,ref:e})});return g.displayName=`Primitive.${b}`,{...a,[b]:g}},{}),h=e.forwardRef((a,b)=>(0,d.jsx)(g.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));h.displayName="Label";var i=c(24224),j=c(4780);let k=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(h,{ref:c,className:(0,j.cn)(k(),a),...b}));l.displayName=h.displayName},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,852,814,300,219],()=>b(b.s=48574));module.exports=c})();