{"version": 3, "sources": ["../../src/classes/HttpProxyAgent.js"], "names": ["HttpProxyAgent", "Agent", "constructor", "args", "protocol", "defaultPort", "createConnection", "configuration", "callback", "socket", "net", "connect", "proxy", "port", "hostname"], "mappings": ";;;;;;;AAEA;;AAKA;;;;AAEA,MAAMA,cAAN,SAA6BC,cAA7B,CAAmC;AACjC;AACA;AACAC,EAAAA,WAAW,CAAE,GAAGC,IAAL,EAAc;AACvB,UAAM,GAAGA,IAAT;AAEA,SAAKC,QAAL,GAAgB,OAAhB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACD;;AAEDC,EAAAA,gBAAgB,CAAEC,aAAF,EAA8CC,QAA9C,EAAgF;AAC9F,UAAMC,MAAM,GAAGC,aAAIC,OAAJ,CACbJ,aAAa,CAACK,KAAd,CAAoBC,IADP,EAEbN,aAAa,CAACK,KAAd,CAAoBE,QAFP,CAAf;;AAKAN,IAAAA,QAAQ,CAAC,IAAD,EAAOC,MAAP,CAAR;AACD;;AAjBgC;;eAoBpBT,c", "sourcesContent": ["// @flow\n\nimport net from 'net';\nimport type {\n  ConnectionCallbackType,\n  ConnectionConfigurationType,\n} from '../types';\nimport Agent from './Agent';\n\nclass HttpProxyAgent extends Agent {\n  // @see https://github.com/sindresorhus/eslint-plugin-unicorn/issues/169#issuecomment-486980290\n  // eslint-disable-next-line unicorn/prevent-abbreviations\n  constructor (...args: *) {\n    super(...args);\n\n    this.protocol = 'http:';\n    this.defaultPort = 80;\n  }\n\n  createConnection (configuration: ConnectionConfigurationType, callback: ConnectionCallbackType) {\n    const socket = net.connect(\n      configuration.proxy.port,\n      configuration.proxy.hostname,\n    );\n\n    callback(null, socket);\n  }\n}\n\nexport default HttpProxyAgent;\n"], "file": "HttpProxyAgent.js"}