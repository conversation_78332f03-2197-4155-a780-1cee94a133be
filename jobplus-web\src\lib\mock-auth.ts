// Mock authentication service for demo purposes
// In production, this would be replaced with real Supabase authentication

interface MockUser {
  id: string;
  email: string;
  user_metadata: {
    username?: string;
  };
}

interface AuthResponse {
  data: {
    user: MockUser | null;
    session: any;
  };
  error: any;
}

class MockAuthService {
  private currentUser: MockUser | null = null;
  private listeners: ((user: MockUser | null) => void)[] = [];

  async signUp(email: string, password: string, metadata?: any): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const user: MockUser = {
      id: Date.now().toString(),
      email,
      user_metadata: metadata || {}
    };
    
    this.currentUser = user;
    this.notifyListeners();
    
    return {
      data: {
        user,
        session: { access_token: 'mock-token' }
      },
      error: null
    };
  }

  async signInWithPassword(email: string, password: string): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock validation - in real app, this would validate against database
    if (email === '<EMAIL>' && password === 'demo123') {
      const user: MockUser = {
        id: '1',
        email,
        user_metadata: { username: '演示用户' }
      };
      
      this.currentUser = user;
      this.notifyListeners();
      
      return {
        data: {
          user,
          session: { access_token: 'mock-token' }
        },
        error: null
      };
    }
    
    return {
      data: { user: null, session: null },
      error: { message: '邮箱或密码错误' }
    };
  }

  async signOut(): Promise<{ error: any }> {
    this.currentUser = null;
    this.notifyListeners();
    return { error: null };
  }

  async resetPasswordForEmail(email: string): Promise<{ error: any }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { error: null };
  }

  async updateUser(updates: any): Promise<{ error: any }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (this.currentUser) {
      this.currentUser = { ...this.currentUser, ...updates };
      this.notifyListeners();
    }
    
    return { error: null };
  }

  async getSession(): Promise<{ data: { session: any } }> {
    return {
      data: {
        session: this.currentUser ? { user: this.currentUser } : null
      }
    };
  }

  onAuthStateChange(callback: (event: string, session: any) => void) {
    const listener = (user: MockUser | null) => {
      callback(user ? 'SIGNED_IN' : 'SIGNED_OUT', user ? { user } : null);
    };
    
    this.listeners.push(listener);
    
    return {
      data: {
        subscription: {
          unsubscribe: () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
              this.listeners.splice(index, 1);
            }
          }
        }
      }
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.currentUser));
  }
}

export const mockAuth = new MockAuthService();

// Create a mock supabase object that matches the expected interface
export const mockSupabase = {
  auth: mockAuth
};
