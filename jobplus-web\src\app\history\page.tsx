"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InterviewStorageService, InterviewSession, InterviewStats } from "@/lib/interview-storage";
import { 
  Brain, 
  Calendar,
  Clock,
  Building,
  Briefcase,
  Star,
  Filter,
  Search,
  Download,
  Trash2,
  Eye,
  Edit,
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  Users,
  Target,
  Award,
  LogOut
} from "lucide-react";
import { PageTransition, FadeIn, StaggeredFadeIn } from "@/components/ui/page-transition";
import { useToastActions } from "@/components/ui/toast";
import { mockAuth } from "@/lib/mock-auth";

export default function HistoryPage() {
  const { user } = useAuth();
  const toast = useToastActions();
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [filteredSessions, setFilteredSessions] = useState<InterviewSession[]>([]);
  const [stats, setStats] = useState<InterviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [sortBy, setSortBy] = useState<"date" | "score" | "duration">("date");
  const [showFilters, setShowFilters] = useState(false);

  const storageService = new InterviewStorageService();

  useEffect(() => {
    loadData();
  }, [user]);

  useEffect(() => {
    applyFilters();
  }, [sessions, searchTerm, filterType, filterStatus, sortBy]);

  const loadData = () => {
    setLoading(true);
    try {
      const userSessions = storageService.getAllSessions(user?.id);
      const userStats = storageService.getStats(user?.id);
      
      setSessions(userSessions);
      setStats(userStats);
    } catch (error) {
      console.error('Failed to load interview data:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...sessions];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(session => 
        session.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.position.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Type filter
    if (filterType !== "all") {
      filtered = filtered.filter(session => session.interviewType === filterType);
    }

    // Status filter
    if (filterStatus !== "all") {
      filtered = filtered.filter(session => session.status === filterStatus);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "date":
          return b.startTime.getTime() - a.startTime.getTime();
        case "score":
          const scoreA = a.summary?.overallScore || 0;
          const scoreB = b.summary?.overallScore || 0;
          return scoreB - scoreA;
        case "duration":
          return b.duration - a.duration;
        default:
          return 0;
      }
    });

    setFilteredSessions(filtered);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'active': return '进行中';
      case 'paused': return '已暂停';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'technical': return '技术面试';
      case 'behavioral': return '行为面试';
      case 'mixed': return '综合面试';
      default: return type;
    }
  };

  const deleteSession = (sessionId: string) => {
    if (confirm('确定要删除这个面试记录吗？此操作无法撤销。')) {
      const success = storageService.deleteSession(sessionId);
      if (success) {
        loadData();
      } else {
        alert('删除失败，请重试。');
      }
    }
  };

  const exportData = () => {
    const data = storageService.exportSessions(user?.id);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `jobplus-interviews-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderStatsCard = (title: string, value: string | number, icon: React.ReactNode, trend?: 'up' | 'down' | 'stable') => (
    <Card className="group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-white to-blue-50 border-blue-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 mb-2">{title}</p>
            <p className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent">{value}</p>
          </div>
          <div className="flex items-center space-x-2">
            {trend && (
              <div className={`p-2 rounded-full ${
                trend === 'up' ? 'text-green-600 bg-green-100' :
                trend === 'down' ? 'text-red-600 bg-red-100' : 'text-gray-600 bg-gray-100'
              }`}>
                {trend === 'up' ? <TrendingUp className="h-4 w-4" /> :
                 trend === 'down' ? <TrendingDown className="h-4 w-4" /> :
                 <Minus className="h-4 w-4" />}
              </div>
            )}
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform">
              {React.cloneElement(icon as React.ReactElement<{ className?: string }>, { className: "h-6 w-6 text-white" })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <ProtectedRoute>
        <PageTransition>
          <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载面试记录中...</p>
          </div>
        </div>
        </PageTransition>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <PageTransition>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Header */}
        <FadeIn direction="down">
          <header className="bg-white/80 backdrop-blur-sm border-b shadow-sm">
            <div className="container mx-auto px-4 py-4 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Brain className="h-8 w-8 text-blue-600" />
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  JobPlus
                </span>
              </div>
              <nav className="flex items-center space-x-4">
                <Link href="/dashboard" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                  控制台
                </Link>
                <Link href="/resume" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                  简历中心
                </Link>
                <Link href="/interview" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                  面试室
                </Link>
                <Link href="/history" className="text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50">
                  面试记录
                </Link>
                <Button variant="ghost" size="icon" onClick={() => mockAuth.signOut()} className="hover:bg-red-50 hover:text-red-600">
                  <LogOut className="h-4 w-4" />
                </Button>
              </nav>
            </div>
          </header>
        </FadeIn>

        <div className="container mx-auto px-4 py-8">
          {/* Page Header */}
          <FadeIn delay={200}>
            <div className="mb-8 text-center">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">
                面试记录
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                查看和管理您的面试历史记录，分析表现并持续改进
              </p>
            </div>
          </FadeIn>

          {/* Stats Overview */}
          {stats && (
            <StaggeredFadeIn delay={400} staggerDelay={100}>
              <div className="grid md:grid-cols-4 gap-6 mb-8">
                {renderStatsCard(
                  "总面试次数",
                  stats.totalSessions,
                  <BarChart3 className="h-5 w-5" />
                )}
                {renderStatsCard(
                  "总时长",
                  formatDuration(stats.totalDuration),
                  <Clock className="h-5 w-5" />
                )}
                {renderStatsCard(
                  "平均评分",
                  `${stats.averageScore}/100`,
                  <Award className="h-5 w-5" />
                )}
                {renderStatsCard(
                  "完成率",
                  `${stats.completionRate}%`,
                  <Target className="h-5 w-5" />
                )}
              </div>
            </StaggeredFadeIn>
          )}

          {/* Filters and Search */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="搜索公司或职位..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="all">所有类型</option>
                    <option value="technical">技术面试</option>
                    <option value="behavioral">行为面试</option>
                    <option value="mixed">综合面试</option>
                  </select>
                  
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="all">所有状态</option>
                    <option value="completed">已完成</option>
                    <option value="active">进行中</option>
                    <option value="paused">已暂停</option>
                    <option value="cancelled">已取消</option>
                  </select>
                  
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="date">按日期排序</option>
                    <option value="score">按评分排序</option>
                    <option value="duration">按时长排序</option>
                  </select>
                  
                  <Button onClick={exportData} variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sessions List */}
          {filteredSessions.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无面试记录</h3>
                <p className="text-gray-600 mb-4">
                  {sessions.length === 0 ? '您还没有进行过面试练习' : '没有找到符合条件的记录'}
                </p>
                <Link href="/interview">
                  <Button>
                    开始第一次面试
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredSessions.map((session) => (
                <Card key={session.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {session.company} - {session.position}
                          </h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                            {getStatusText(session.status)}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-6 text-sm text-gray-600 mb-3">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(session.startTime)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{formatDuration(session.duration)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Briefcase className="h-4 w-4" />
                            <span>{getTypeText(session.interviewType)}</span>
                          </div>
                          {session.summary?.overallScore && (
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4" />
                              <span>{session.summary.overallScore}/100</span>
                            </div>
                          )}
                        </div>

                        {session.summary && (
                          <div className="bg-gray-50 p-3 rounded-lg mb-3">
                            <div className="grid md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-700">优势：</span>
                                <span className="text-gray-600">
                                  {session.summary.strongPoints.join('、') || '暂无'}
                                </span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-700">改进建议：</span>
                                <span className="text-gray-600">
                                  {session.summary.improvementAreas.join('、') || '暂无'}
                                </span>
                              </div>
                            </div>
                          </div>
                        )}

                        {session.userNotes && (
                          <div className="bg-blue-50 p-3 rounded-lg">
                            <span className="font-medium text-blue-900">备注：</span>
                            <span className="text-blue-800">{session.userNotes}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <Link href={`/history/${session.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            查看详情
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deleteSession(session.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
      </PageTransition>
    </ProtectedRoute>
  );
}
