[{"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\forgot-password\\page.tsx": "1", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\login\\page.tsx": "2", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\register\\page.tsx": "3", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\reset-password\\page.tsx": "4", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\dashboard\\page.tsx": "5", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\page.tsx": "6", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\[id]\\page.tsx": "7", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\interview\\page.tsx": "8", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\layout.tsx": "9", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\page.tsx": "10", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\resume\\page.tsx": "11", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\settings\\page.tsx": "12", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ProtectedRoute.tsx": "13", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\button.tsx": "14", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\card.tsx": "15", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\input.tsx": "16", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\label.tsx": "17", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\loading.tsx": "18", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\modal.tsx": "19", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\page-transition.tsx": "20", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\toast.tsx": "21", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\contexts\\AuthContext.tsx": "22", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\hooks\\useElectron.ts": "23", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\ai-assistant.ts": "24", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\audio-processor.ts": "25", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\interview-storage.ts": "26", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\mock-auth.ts": "27", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\supabase.ts": "28", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\utils.ts": "29", "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\types\\electron.d.ts": "30"}, {"size": 4094, "mtime": 1754143290306, "results": "31", "hashOfConfig": "32"}, {"size": 4456, "mtime": 1754146568148, "results": "33", "hashOfConfig": "32"}, {"size": 7478, "mtime": 1754146671498, "results": "34", "hashOfConfig": "32"}, {"size": 5955, "mtime": 1754143322746, "results": "35", "hashOfConfig": "32"}, {"size": 13665, "mtime": 1754147031358, "results": "36", "hashOfConfig": "32"}, {"size": 18659, "mtime": 1754148053123, "results": "37", "hashOfConfig": "32"}, {"size": 19510, "mtime": 1754146927608, "results": "38", "hashOfConfig": "32"}, {"size": 30425, "mtime": 1754147787210, "results": "39", "hashOfConfig": "32"}, {"size": 2222, "mtime": 1754145931986, "results": "40", "hashOfConfig": "32"}, {"size": 7382, "mtime": 1754142648566, "results": "41", "hashOfConfig": "32"}, {"size": 27489, "mtime": 1754147919273, "results": "42", "hashOfConfig": "32"}, {"size": 11626, "mtime": 1754143013615, "results": "43", "hashOfConfig": "32"}, {"size": 829, "mtime": 1754142898025, "results": "44", "hashOfConfig": "32"}, {"size": 1835, "mtime": 1754142542178, "results": "45", "hashOfConfig": "32"}, {"size": 1877, "mtime": 1754142560237, "results": "46", "hashOfConfig": "32"}, {"size": 824, "mtime": 1754142549578, "results": "47", "hashOfConfig": "32"}, {"size": 710, "mtime": 1754142566886, "results": "48", "hashOfConfig": "32"}, {"size": 4458, "mtime": 1754145747162, "results": "49", "hashOfConfig": "32"}, {"size": 6291, "mtime": 1754145817282, "results": "50", "hashOfConfig": "32"}, {"size": 6237, "mtime": 1754148165871, "results": "51", "hashOfConfig": "32"}, {"size": 5835, "mtime": 1754146342541, "results": "52", "hashOfConfig": "32"}, {"size": 1555, "mtime": 1754143212247, "results": "53", "hashOfConfig": "32"}, {"size": 1219, "mtime": 1754148756181, "results": "54", "hashOfConfig": "32"}, {"size": 15879, "mtime": 1754143892584, "results": "55", "hashOfConfig": "32"}, {"size": 8692, "mtime": 1754143782312, "results": "56", "hashOfConfig": "32"}, {"size": 13383, "mtime": 1754144300623, "results": "57", "hashOfConfig": "32"}, {"size": 3356, "mtime": 1754143191396, "results": "58", "hashOfConfig": "32"}, {"size": 1752, "mtime": 1754143168535, "results": "59", "hashOfConfig": "32"}, {"size": 939, "mtime": 1754142469068, "results": "60", "hashOfConfig": "32"}, {"size": 692, "mtime": 1754148744139, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1gk7it0", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\forgot-password\\page.tsx", ["152"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\login\\page.tsx", ["153", "154", "155", "156"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\register\\page.tsx", ["157", "158", "159", "160"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\auth\\reset-password\\page.tsx", ["161", "162"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\dashboard\\page.tsx", ["163", "164", "165", "166", "167", "168"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\page.tsx", ["169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\[id]\\page.tsx", ["183", "184", "185", "186", "187", "188", "189", "190", "191"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\interview\\page.tsx", ["192", "193", "194", "195", "196", "197", "198", "199", "200", "201"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\layout.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\page.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\resume\\page.tsx", ["202", "203", "204", "205"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\settings\\page.tsx", ["206", "207", "208"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\button.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\card.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\input.tsx", ["209"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\label.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\loading.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\modal.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\page-transition.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\toast.tsx", ["210"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\contexts\\AuthContext.tsx", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\hooks\\useElectron.ts", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\ai-assistant.ts", ["211", "212"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\audio-processor.ts", ["213", "214"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\interview-storage.ts", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\mock-auth.ts", ["215", "216", "217", "218", "219", "220", "221", "222", "223", "224"], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\supabase.ts", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\lib\\utils.ts", [], [], "D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\types\\electron.d.ts", ["225", "226", "227"], [], {"ruleId": "228", "severity": 1, "message": "229", "line": 31, "column": 14, "nodeType": null, "messageId": "230", "endLine": 31, "endColumn": 17}, {"ruleId": "228", "severity": 1, "message": "231", "line": 12, "column": 26, "nodeType": null, "messageId": "230", "endLine": 12, "endColumn": 32}, {"ruleId": "228", "severity": 1, "message": "232", "line": 22, "column": 9, "nodeType": null, "messageId": "230", "endLine": 22, "endColumn": 14}, {"ruleId": "228", "severity": 1, "message": "233", "line": 30, "column": 15, "nodeType": null, "messageId": "230", "endLine": 30, "endColumn": 19}, {"ruleId": "228", "severity": 1, "message": "229", "line": 37, "column": 14, "nodeType": null, "messageId": "230", "endLine": 37, "endColumn": 17}, {"ruleId": "228", "severity": 1, "message": "231", "line": 12, "column": 26, "nodeType": null, "messageId": "230", "endLine": 12, "endColumn": 32}, {"ruleId": "228", "severity": 1, "message": "232", "line": 28, "column": 9, "nodeType": null, "messageId": "230", "endLine": 28, "endColumn": 14}, {"ruleId": "228", "severity": 1, "message": "233", "line": 61, "column": 15, "nodeType": null, "messageId": "230", "endLine": 61, "endColumn": 19}, {"ruleId": "228", "severity": 1, "message": "229", "line": 72, "column": 14, "nodeType": null, "messageId": "230", "endLine": 72, "endColumn": 17}, {"ruleId": "228", "severity": 1, "message": "234", "line": 21, "column": 9, "nodeType": null, "messageId": "230", "endLine": 21, "endColumn": 21}, {"ruleId": "228", "severity": 1, "message": "229", "line": 70, "column": 14, "nodeType": null, "messageId": "230", "endLine": 70, "endColumn": 17}, {"ruleId": "228", "severity": 1, "message": "235", "line": 3, "column": 20, "nodeType": null, "messageId": "230", "endLine": 3, "endColumn": 29}, {"ruleId": "228", "severity": 1, "message": "236", "line": 15, "column": 3, "nodeType": null, "messageId": "230", "endLine": 15, "endColumn": 11}, {"ruleId": "228", "severity": 1, "message": "237", "line": 20, "column": 34, "nodeType": null, "messageId": "230", "endLine": 20, "endColumn": 49}, {"ruleId": "228", "severity": 1, "message": "238", "line": 21, "column": 10, "nodeType": null, "messageId": "230", "endLine": 21, "endColumn": 25}, {"ruleId": "228", "severity": 1, "message": "239", "line": 25, "column": 17, "nodeType": null, "messageId": "230", "endLine": 25, "endColumn": 25}, {"ruleId": "228", "severity": 1, "message": "240", "line": 32, "column": 26, "nodeType": null, "messageId": "230", "endLine": 32, "endColumn": 43}, {"ruleId": "228", "severity": 1, "message": "241", "line": 9, "column": 10, "nodeType": null, "messageId": "230", "endLine": 9, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "242", "line": 10, "column": 29, "nodeType": null, "messageId": "230", "endLine": 10, "endColumn": 44}, {"ruleId": "228", "severity": 1, "message": "243", "line": 10, "column": 46, "nodeType": null, "messageId": "230", "endLine": 10, "endColumn": 56}, {"ruleId": "228", "severity": 1, "message": "244", "line": 10, "column": 58, "nodeType": null, "messageId": "230", "endLine": 10, "endColumn": 67}, {"ruleId": "228", "severity": 1, "message": "245", "line": 16, "column": 3, "nodeType": null, "messageId": "230", "endLine": 16, "endColumn": 11}, {"ruleId": "228", "severity": 1, "message": "246", "line": 19, "column": 3, "nodeType": null, "messageId": "230", "endLine": 19, "endColumn": 9}, {"ruleId": "228", "severity": 1, "message": "247", "line": 24, "column": 3, "nodeType": null, "messageId": "230", "endLine": 24, "endColumn": 7}, {"ruleId": "228", "severity": 1, "message": "248", "line": 25, "column": 3, "nodeType": null, "messageId": "230", "endLine": 25, "endColumn": 12}, {"ruleId": "228", "severity": 1, "message": "232", "line": 41, "column": 9, "nodeType": null, "messageId": "230", "endLine": 41, "endColumn": 14}, {"ruleId": "228", "severity": 1, "message": "249", "line": 50, "column": 10, "nodeType": null, "messageId": "230", "endLine": 50, "endColumn": 21}, {"ruleId": "228", "severity": 1, "message": "250", "line": 50, "column": 23, "nodeType": null, "messageId": "230", "endLine": 50, "endColumn": 37}, {"ruleId": "251", "severity": 1, "message": "252", "line": 56, "column": 6, "nodeType": "253", "endLine": 56, "endColumn": 12, "suggestions": "254"}, {"ruleId": "251", "severity": 1, "message": "255", "line": 60, "column": 6, "nodeType": "253", "endLine": 60, "endColumn": 62, "suggestions": "256"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 350, "column": 66, "nodeType": "259", "messageId": "260", "endLine": 350, "endColumn": 69, "suggestions": "261"}, {"ruleId": "228", "severity": 1, "message": "262", "line": 9, "column": 10, "nodeType": null, "messageId": "230", "endLine": 9, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "241", "line": 10, "column": 10, "nodeType": null, "messageId": "230", "endLine": 10, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "263", "line": 28, "column": 3, "nodeType": null, "messageId": "230", "endLine": 28, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "264", "line": 31, "column": 3, "nodeType": null, "messageId": "230", "endLine": 31, "endColumn": 13}, {"ruleId": "228", "severity": 1, "message": "231", "line": 36, "column": 26, "nodeType": null, "messageId": "230", "endLine": 36, "endColumn": 32}, {"ruleId": "228", "severity": 1, "message": "265", "line": 42, "column": 11, "nodeType": null, "messageId": "230", "endLine": 42, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "232", "line": 43, "column": 9, "nodeType": null, "messageId": "230", "endLine": 43, "endColumn": 14}, {"ruleId": "251", "severity": 1, "message": "266", "line": 54, "column": 6, "nodeType": "253", "endLine": 54, "endColumn": 17, "suggestions": "267"}, {"ruleId": "228", "severity": 1, "message": "268", "line": 166, "column": 53, "nodeType": null, "messageId": "230", "endLine": 166, "endColumn": 58}, {"ruleId": "228", "severity": 1, "message": "269", "line": 12, "column": 41, "nodeType": null, "messageId": "230", "endLine": 12, "endColumn": 51}, {"ruleId": "228", "severity": 1, "message": "270", "line": 13, "column": 70, "nodeType": null, "messageId": "230", "endLine": 13, "endColumn": 86}, {"ruleId": "228", "severity": 1, "message": "271", "line": 19, "column": 3, "nodeType": null, "messageId": "230", "endLine": 19, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "248", "line": 22, "column": 3, "nodeType": null, "messageId": "230", "endLine": 22, "endColumn": 12}, {"ruleId": "228", "severity": 1, "message": "236", "line": 23, "column": 3, "nodeType": null, "messageId": "230", "endLine": 23, "endColumn": 11}, {"ruleId": "228", "severity": 1, "message": "272", "line": 25, "column": 3, "nodeType": null, "messageId": "230", "endLine": 25, "endColumn": 10}, {"ruleId": "228", "severity": 1, "message": "264", "line": 33, "column": 3, "nodeType": null, "messageId": "230", "endLine": 33, "endColumn": 13}, {"ruleId": "228", "severity": 1, "message": "273", "line": 34, "column": 3, "nodeType": null, "messageId": "230", "endLine": 34, "endColumn": 7}, {"ruleId": "257", "severity": 2, "message": "258", "line": 497, "column": 108, "nodeType": "259", "messageId": "260", "endLine": 497, "endColumn": 111, "suggestions": "274"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 509, "column": 105, "nodeType": "259", "messageId": "260", "endLine": 509, "endColumn": 108, "suggestions": "275"}, {"ruleId": "228", "severity": 1, "message": "248", "line": 19, "column": 3, "nodeType": null, "messageId": "230", "endLine": 19, "endColumn": 12}, {"ruleId": "228", "severity": 1, "message": "232", "line": 73, "column": 9, "nodeType": null, "messageId": "230", "endLine": 73, "endColumn": 14}, {"ruleId": "228", "severity": 1, "message": "276", "line": 75, "column": 10, "nodeType": null, "messageId": "230", "endLine": 75, "endColumn": 21}, {"ruleId": "257", "severity": 2, "message": "258", "line": 197, "column": 81, "nodeType": "259", "messageId": "260", "endLine": 197, "endColumn": 84, "suggestions": "277"}, {"ruleId": "228", "severity": 1, "message": "278", "line": 14, "column": 3, "nodeType": null, "messageId": "230", "endLine": 14, "endColumn": 7}, {"ruleId": "228", "severity": 1, "message": "229", "line": 54, "column": 14, "nodeType": null, "messageId": "230", "endLine": 54, "endColumn": 17}, {"ruleId": "228", "severity": 1, "message": "229", "line": 69, "column": 14, "nodeType": null, "messageId": "230", "endLine": 69, "endColumn": 17}, {"ruleId": "279", "severity": 2, "message": "280", "line": 5, "column": 18, "nodeType": "281", "messageId": "282", "endLine": 5, "endColumn": 28, "suggestions": "283"}, {"ruleId": "251", "severity": 1, "message": "284", "line": 59, "column": 6, "nodeType": "253", "endLine": 59, "endColumn": 8, "suggestions": "285"}, {"ruleId": "286", "severity": 2, "message": "287", "line": 199, "column": 9, "nodeType": "281", "messageId": "288", "endLine": 199, "endColumn": 30, "fix": "289"}, {"ruleId": "228", "severity": 1, "message": "290", "line": 284, "column": 83, "nodeType": null, "messageId": "230", "endLine": 284, "endColumn": 91}, {"ruleId": "257", "severity": 2, "message": "258", "line": 60, "column": 66, "nodeType": "259", "messageId": "260", "endLine": 60, "endColumn": 69, "suggestions": "291"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 288, "column": 42, "nodeType": "259", "messageId": "260", "endLine": 288, "endColumn": 45, "suggestions": "292"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 15, "column": 14, "nodeType": "259", "messageId": "260", "endLine": 15, "endColumn": 17, "suggestions": "293"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 17, "column": 10, "nodeType": "259", "messageId": "260", "endLine": 17, "endColumn": 13, "suggestions": "294"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 24, "column": 60, "nodeType": "259", "messageId": "260", "endLine": 24, "endColumn": 63, "suggestions": "295"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 76, "column": 37, "nodeType": "259", "messageId": "260", "endLine": 76, "endColumn": 40, "suggestions": "296"}, {"ruleId": "228", "severity": 1, "message": "297", "line": 82, "column": 31, "nodeType": null, "messageId": "230", "endLine": 82, "endColumn": 36}, {"ruleId": "257", "severity": 2, "message": "258", "line": 82, "column": 64, "nodeType": "259", "messageId": "260", "endLine": 82, "endColumn": 67, "suggestions": "298"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 88, "column": 29, "nodeType": "259", "messageId": "260", "endLine": 88, "endColumn": 32, "suggestions": "299"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 88, "column": 52, "nodeType": "259", "messageId": "260", "endLine": 88, "endColumn": 55, "suggestions": "300"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 100, "column": 50, "nodeType": "259", "messageId": "260", "endLine": 100, "endColumn": 53, "suggestions": "301"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 108, "column": 56, "nodeType": "259", "messageId": "260", "endLine": 108, "endColumn": 59, "suggestions": "302"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 3, "column": 29, "nodeType": "259", "messageId": "260", "endLine": 3, "endColumn": 32, "suggestions": "303"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 3, "column": 45, "nodeType": "259", "messageId": "260", "endLine": 3, "endColumn": 48, "suggestions": "304"}, {"ruleId": "257", "severity": 2, "message": "258", "line": 8, "column": 20, "nodeType": "259", "messageId": "260", "endLine": 8, "endColumn": 23, "suggestions": "305"}, "@typescript-eslint/no-unused-vars", "'err' is defined but never used.", "unusedVar", "'FadeIn' is defined but never used.", "'toast' is assigned a value but never used.", "'data' is assigned a value but never used.", "'searchParams' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Settings' is defined but never used.", "'StaggeredFadeIn' is defined but never used.", "'useToastActions' is defined but never used.", "'setStats' is assigned a value but never used.", "'setRecentSessions' is assigned a value but never used.", "'Label' is defined but never used.", "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Building' is defined but never used.", "'Filter' is defined but never used.", "'Edit' is defined but never used.", "'ArrowLeft' is defined but never used.", "'showFilters' is assigned a value but never used.", "'setShowFilters' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", "ArrayExpression", ["306"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["307"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["308", "309"], "'Input' is defined but never used.", "'Share' is defined but never used.", "'TrendingUp' is defined but never used.", "'user' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSession'. Either include it or remove the dependency array.", ["310"], "'index' is defined but never used.", "'AIResponse' is defined but never used.", "'InterviewMessage' is defined but never used.", "'Pause' is defined but never used.", "'VolumeX' is defined but never used.", "'Save' is defined but never used.", ["311", "312"], ["313", "314"], "'isUploading' is assigned a value but never used.", ["315", "316"], "'Mail' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["317"], "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["318"], "prefer-const", "'suggestions' is never reassigned. Use 'const' instead.", "useConst", {"range": "319", "text": "320"}, "'question' is defined but never used.", ["321", "322"], ["323", "324"], ["325", "326"], ["327", "328"], ["329", "330"], ["331", "332"], "'email' is defined but never used.", ["333", "334"], ["335", "336"], ["337", "338"], ["339", "340"], ["341", "342"], ["343", "344"], ["345", "346"], ["347", "348"], {"desc": "349", "fix": "350"}, {"desc": "351", "fix": "352"}, {"messageId": "353", "fix": "354", "desc": "355"}, {"messageId": "356", "fix": "357", "desc": "358"}, {"desc": "359", "fix": "360"}, {"messageId": "353", "fix": "361", "desc": "355"}, {"messageId": "356", "fix": "362", "desc": "358"}, {"messageId": "353", "fix": "363", "desc": "355"}, {"messageId": "356", "fix": "364", "desc": "358"}, {"messageId": "353", "fix": "365", "desc": "355"}, {"messageId": "356", "fix": "366", "desc": "358"}, {"messageId": "367", "fix": "368", "desc": "369"}, {"desc": "370", "fix": "371"}, [6118, 6149], "const suggestions: string[] = [];", {"messageId": "353", "fix": "372", "desc": "355"}, {"messageId": "356", "fix": "373", "desc": "358"}, {"messageId": "353", "fix": "374", "desc": "355"}, {"messageId": "356", "fix": "375", "desc": "358"}, {"messageId": "353", "fix": "376", "desc": "355"}, {"messageId": "356", "fix": "377", "desc": "358"}, {"messageId": "353", "fix": "378", "desc": "355"}, {"messageId": "356", "fix": "379", "desc": "358"}, {"messageId": "353", "fix": "380", "desc": "355"}, {"messageId": "356", "fix": "381", "desc": "358"}, {"messageId": "353", "fix": "382", "desc": "355"}, {"messageId": "356", "fix": "383", "desc": "358"}, {"messageId": "353", "fix": "384", "desc": "355"}, {"messageId": "356", "fix": "385", "desc": "358"}, {"messageId": "353", "fix": "386", "desc": "355"}, {"messageId": "356", "fix": "387", "desc": "358"}, {"messageId": "353", "fix": "388", "desc": "355"}, {"messageId": "356", "fix": "389", "desc": "358"}, {"messageId": "353", "fix": "390", "desc": "355"}, {"messageId": "356", "fix": "391", "desc": "358"}, {"messageId": "353", "fix": "392", "desc": "355"}, {"messageId": "356", "fix": "393", "desc": "358"}, {"messageId": "353", "fix": "394", "desc": "355"}, {"messageId": "356", "fix": "395", "desc": "358"}, {"messageId": "353", "fix": "396", "desc": "355"}, {"messageId": "356", "fix": "397", "desc": "358"}, {"messageId": "353", "fix": "398", "desc": "355"}, {"messageId": "356", "fix": "399", "desc": "358"}, "Update the dependencies array to be: [loadData, user]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [sessions, searchTerm, filterType, filterStatus, sortBy, applyFilters]", {"range": "402", "text": "403"}, "suggestUnknown", {"range": "404", "text": "405"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "406", "text": "407"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [loadSession, params.id]", {"range": "408", "text": "409"}, {"range": "410", "text": "405"}, {"range": "411", "text": "407"}, {"range": "412", "text": "405"}, {"range": "413", "text": "407"}, {"range": "414", "text": "405"}, {"range": "415", "text": "407"}, "replaceEmptyInterfaceWithSuper", {"range": "416", "text": "417"}, "Replace empty interface with a type alias.", "Update the dependencies array to be: [removeToast]", {"range": "418", "text": "419"}, {"range": "420", "text": "405"}, {"range": "421", "text": "407"}, {"range": "422", "text": "405"}, {"range": "423", "text": "407"}, {"range": "424", "text": "405"}, {"range": "425", "text": "407"}, {"range": "426", "text": "405"}, {"range": "427", "text": "407"}, {"range": "428", "text": "405"}, {"range": "429", "text": "407"}, {"range": "430", "text": "405"}, {"range": "431", "text": "407"}, {"range": "432", "text": "405"}, {"range": "433", "text": "407"}, {"range": "434", "text": "405"}, {"range": "435", "text": "407"}, {"range": "436", "text": "405"}, {"range": "437", "text": "407"}, {"range": "438", "text": "405"}, {"range": "439", "text": "407"}, {"range": "440", "text": "405"}, {"range": "441", "text": "407"}, {"range": "442", "text": "405"}, {"range": "443", "text": "407"}, {"range": "444", "text": "405"}, {"range": "445", "text": "407"}, {"range": "446", "text": "405"}, {"range": "447", "text": "407"}, [1785, 1791], "[loadData, user]", [1840, 1896], "[sessions, searchTerm, filterType, filterStatus, sortBy, applyFilters]", [12536, 12539], "unknown", [12536, 12539], "never", [1522, 1533], "[loadSession, params.id]", [17224, 17227], [17224, 17227], [17993, 17996], [17993, 17996], [4847, 4850], [4847, 4850], [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1623, 1625], "[removeToast]", [1711, 1714], [1711, 1714], [7879, 7882], [7879, 7882], [302, 305], [302, 305], [321, 324], [321, 324], [523, 526], [523, 526], [1755, 1758], [1755, 1758], [1917, 1920], [1917, 1920], [2074, 2077], [2074, 2077], [2097, 2100], [2097, 2100], [2411, 2414], [2411, 2414], [2594, 2597], [2594, 2597], [89, 92], [89, 92], [105, 108], [105, 108], [263, 266], [263, 266]]