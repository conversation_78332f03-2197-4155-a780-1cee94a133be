(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[429],{117:(e,s,a)=>{Promise.resolve().then(a.bind(a,7643))},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(5155),r=a(2115),l=a(9434);let n=r.forwardRef((e,s)=>{let{className:a,type:r,...n}=e;return(0,t.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...n})});n.displayName="Input"},7643:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>R});var t=a(5155),r=a(2115),l=a(6874),n=a.n(l),i=a(283),c=a(9053),d=a(285),o=a(2523),x=a(6695),m=a(9148),u=a(3109),h=a(8500),p=a(7712),j=a(9376),g=a(4835),v=a(2713),b=a(4186),f=a(9037),N=a(6785),y=a(7924),w=a(1788),A=a(7580),S=a(9074),C=a(7576),k=a(8564),_=a(2657),E=a(2525),L=a(688),T=a(6982),O=a(7292);function R(){let{user:e}=(0,i.A)();(0,T.EM)();let[s,a]=(0,r.useState)([]),[l,R]=(0,r.useState)([]),[Z,$]=(0,r.useState)(null),[z,D]=(0,r.useState)(!0),[U,W]=(0,r.useState)(""),[M,P]=(0,r.useState)("all"),[I,q]=(0,r.useState)("all"),[B,F]=(0,r.useState)("date"),[J,G]=(0,r.useState)(!1),H=new m.E;(0,r.useEffect)(()=>{K()},[e]),(0,r.useEffect)(()=>{Q()},[s,U,M,I,B]);let K=()=>{D(!0);try{let s=H.getAllSessions(null==e?void 0:e.id),t=H.getStats(null==e?void 0:e.id);a(s),$(t)}catch(e){console.error("Failed to load interview data:",e)}finally{D(!1)}},Q=()=>{let e=[...s];U&&(e=e.filter(e=>e.company.toLowerCase().includes(U.toLowerCase())||e.position.toLowerCase().includes(U.toLowerCase()))),"all"!==M&&(e=e.filter(e=>e.interviewType===M)),"all"!==I&&(e=e.filter(e=>e.status===I)),e.sort((e,s)=>{switch(B){case"date":return s.startTime.getTime()-e.startTime.getTime();case"score":var a,t;let r=(null==(a=e.summary)?void 0:a.overallScore)||0;return((null==(t=s.summary)?void 0:t.overallScore)||0)-r;case"duration":return s.duration-e.duration;default:return 0}}),R(e)},V=e=>{let s=Math.floor(e/3600),a=Math.floor(e%3600/60);return s>0?"".concat(s,"小时").concat(a,"分钟"):"".concat(a,"分钟")},X=(e,s,a,l)=>(0,t.jsx)(x.Zp,{className:"group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-white to-blue-50 border-blue-200",children:(0,t.jsx)(x.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:e}),(0,t.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent",children:s})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[l&&(0,t.jsx)("div",{className:"p-2 rounded-full ".concat("up"===l?"text-green-600 bg-green-100":"down"===l?"text-red-600 bg-red-100":"text-gray-600 bg-gray-100"),children:"up"===l?(0,t.jsx)(u.A,{className:"h-4 w-4"}):"down"===l?(0,t.jsx)(h.A,{className:"h-4 w-4"}):(0,t.jsx)(p.A,{className:"h-4 w-4"})}),(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform",children:r.cloneElement(a,{className:"h-6 w-6 text-white"})})]})]})})});return z?(0,t.jsx)(c.A,{children:(0,t.jsx)(L.Z_,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"加载面试记录中..."})]})})})}):(0,t.jsx)(c.A,{children:(0,t.jsx)(L.Z_,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,t.jsx)(L._A,{direction:"down",children:(0,t.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,t.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"控制台"}),(0,t.jsx)(n(),{href:"/resume",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"简历中心"}),(0,t.jsx)(n(),{href:"/interview",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试室"}),(0,t.jsx)(n(),{href:"/history",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"面试记录"}),(0,t.jsx)(d.$,{variant:"ghost",size:"icon",onClick:()=>O.f.signOut(),className:"hover:bg-red-50 hover:text-red-600",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]})]})})}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)(L._A,{delay:200,children:(0,t.jsxs)("div",{className:"mb-8 text-center",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:"面试记录"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"查看和管理您的面试历史记录，分析表现并持续改进"})]})}),Z&&(0,t.jsx)(L.Oq,{delay:400,staggerDelay:100,children:(0,t.jsxs)("div",{className:"grid md:grid-cols-4 gap-6 mb-8",children:[X("总面试次数",Z.totalSessions,(0,t.jsx)(v.A,{className:"h-5 w-5"})),X("总时长",V(Z.totalDuration),(0,t.jsx)(b.A,{className:"h-5 w-5"})),X("平均评分","".concat(Z.averageScore,"/100"),(0,t.jsx)(f.A,{className:"h-5 w-5"})),X("完成率","".concat(Z.completionRate,"%"),(0,t.jsx)(N.A,{className:"h-5 w-5"}))]})}),(0,t.jsx)(x.Zp,{className:"mb-6",children:(0,t.jsx)(x.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"搜索公司或职位...",value:U,onChange:e=>W(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:M,onChange:e=>P(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"all",children:"所有类型"}),(0,t.jsx)("option",{value:"technical",children:"技术面试"}),(0,t.jsx)("option",{value:"behavioral",children:"行为面试"}),(0,t.jsx)("option",{value:"mixed",children:"综合面试"})]}),(0,t.jsxs)("select",{value:I,onChange:e=>q(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"all",children:"所有状态"}),(0,t.jsx)("option",{value:"completed",children:"已完成"}),(0,t.jsx)("option",{value:"active",children:"进行中"}),(0,t.jsx)("option",{value:"paused",children:"已暂停"}),(0,t.jsx)("option",{value:"cancelled",children:"已取消"})]}),(0,t.jsxs)("select",{value:B,onChange:e=>F(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"date",children:"按日期排序"}),(0,t.jsx)("option",{value:"score",children:"按评分排序"}),(0,t.jsx)("option",{value:"duration",children:"按时长排序"})]}),(0,t.jsxs)(d.$,{onClick:()=>{let s=new Blob([H.exportSessions(null==e?void 0:e.id)],{type:"application/json"}),a=URL.createObjectURL(s),t=document.createElement("a");t.href=a,t.download="jobplus-interviews-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(a)},variant:"outline",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"导出"]})]})]})})}),0===l.length?(0,t.jsx)(x.Zp,{children:(0,t.jsxs)(x.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无面试记录"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:0===s.length?"您还没有进行过面试练习":"没有找到符合条件的记录"}),(0,t.jsx)(n(),{href:"/interview",children:(0,t.jsx)(d.$,{children:"开始第一次面试"})})]})}):(0,t.jsx)("div",{className:"space-y-4",children:l.map(e=>{var s;return(0,t.jsx)(x.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,t.jsx)(x.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[e.company," - ",e.position]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"active":return"text-blue-600 bg-blue-100";case"paused":return"text-yellow-600 bg-yellow-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(e.status)),children:(e=>{switch(e){case"completed":return"已完成";case"active":return"进行中";case"paused":return"已暂停";case"cancelled":return"已取消";default:return"未知"}})(e.status)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-600 mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.startTime.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:V(e.duration)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:(e=>{switch(e){case"technical":return"技术面试";case"behavioral":return"行为面试";case"mixed":return"综合面试";default:return e}})(e.interviewType)})]}),(null==(s=e.summary)?void 0:s.overallScore)&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.summary.overallScore,"/100"]})]})]}),e.summary&&(0,t.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg mb-3",children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"优势："}),(0,t.jsx)("span",{className:"text-gray-600",children:e.summary.strongPoints.join("、")||"暂无"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"改进建议："}),(0,t.jsx)("span",{className:"text-gray-600",children:e.summary.improvementAreas.join("、")||"暂无"})]})]})}),e.userNotes&&(0,t.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium text-blue-900",children:"备注："}),(0,t.jsx)("span",{className:"text-blue-800",children:e.userNotes})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,t.jsx)(n(),{href:"/history/".concat(e.id),children:(0,t.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"查看详情"]})}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>{var s;return s=e.id,void(confirm("确定要删除这个面试记录吗？此操作无法撤销。")&&(H.deleteSession(s)?K():alert("删除失败，请重试。")))},className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})]})})},e.id)})})]})]})})})}}},e=>{e.O(0,[598,874,256,642,441,964,358],()=>e(e.s=117)),_N_E=e.O()}]);