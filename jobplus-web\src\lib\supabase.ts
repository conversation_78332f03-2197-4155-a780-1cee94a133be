import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'

// Create a mock client for demo purposes if no real credentials are provided
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  username: string
  created_at: string
  updated_at: string
}

export interface Resume {
  id: string
  user_id: string
  personal_info: {
    name: string
    phone: string
    email: string
    city: string
  }
  education: Array<{
    school: string
    degree: string
    major: string
    start_date: string
    end_date: string
  }>
  work_experience: Array<{
    company: string
    position: string
    start_date: string
    end_date: string
    responsibilities: string[]
  }>
  projects: Array<{
    name: string
    role: string
    description: string
    technologies: string[]
  }>
  skills: {
    programming_languages: string[]
    frameworks: string[]
    databases: string[]
    tools: string[]
  }
  created_at: string
  updated_at: string
}

export interface InterviewSession {
  id: string
  user_id: string
  company: string
  position: string
  status: 'active' | 'completed'
  conversation: Array<{
    id: string
    type: 'question' | 'answer'
    content: string
    timestamp: string
  }>
  notes: string
  duration: number
  created_at: string
  updated_at: string
}

export interface UserUsage {
  id: string
  user_id: string
  total_sessions: number
  total_duration: number
  remaining_credits: number
  plan_type: 'free' | 'premium'
  created_at: string
  updated_at: string
}
