"use strict";(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[642],{283:(e,t,r)=>{r.d(t,{A:()=>i,AuthProvider:()=>l});var s=r(5155),n=r(2115),a=r(7292);let o=(0,n.createContext)({user:null,loading:!0,signOut:async()=>{}}),i=()=>{let e=(0,n.useContext)(o);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},l=e=>{let{children:t}=e,[r,i]=(0,n.useState)(null),[l,u]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{var e;let{data:{session:t}}=await a.j.auth.getSession();i(null!=(e=null==t?void 0:t.user)?e:null),u(!1)})();let{data:{subscription:e}}=a.j.auth.onAuthStateChange(async(e,t)=>{var r;i(null!=(r=null==t?void 0:t.user)?r:null),u(!1)});return()=>e.unsubscribe()},[]);let c=async()=>{await a.j.auth.signOut()};return(0,s.jsx)(o.Provider,{value:{user:r,loading:l,signOut:c},children:t})}},285:(e,t,r)=>{r.d(t,{$:()=>u});var s=r(5155),n=r(2115),a=r(4624),o=r(2085),i=r(9434);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef((e,t)=>{let{className:r,variant:n,size:o,asChild:u=!1,...c}=e,d=u?a.DX:"button";return(0,s.jsx)(d,{className:(0,i.cn)(l({variant:n,size:o,className:r})),ref:t,...c})});u.displayName="Button"},688:(e,t,r)=>{r.d(t,{Oq:()=>l,Z_:()=>o,_A:()=>i});var s=r(5155),n=r(2115),a=r(9434);function o(e){let{children:t,className:r,delay:o=0}=e,[i,l]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=setTimeout(()=>{l(!0)},o);return()=>clearTimeout(e)},[o]),(0,s.jsx)("div",{className:(0,a.cn)("transition-all duration-500 ease-out",i?"opacity-100 translate-y-0":"opacity-0 translate-y-4",r),children:t})}function i(e){let{children:t,direction:r="up",delay:o=0,duration:i=500,className:l}=e,[u,c]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=setTimeout(()=>{c(!0)},o);return()=>clearTimeout(e)},[o]),(0,s.jsx)("div",{className:(0,a.cn)("transition-all ease-out",u?"opacity-100 translate-x-0 translate-y-0":"opacity-0 ".concat({up:"translate-y-4",down:"-translate-y-4",left:"translate-x-4",right:"-translate-x-4"}[r]),l),style:{transitionDuration:"".concat(i,"ms")},children:t})}function l(e){let{children:t,delay:r=0,staggerDelay:a=100,className:o}=e,l=n.Children.toArray(t);return(0,s.jsx)("div",{className:o,children:l.map((e,t)=>(0,s.jsx)(i,{delay:r+t*a,children:e},t))})}},6695:(e,t,r)=>{r.d(t,{BT:()=>u,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i});var s=r(5155),n=r(2115),a=r(9434);let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});o.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...n})});i.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});l.displayName="CardTitle";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...n})});u.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...n})});c.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"},6982:(e,t,r)=>{r.d(t,{EM:()=>p,ToastProvider:()=>m});var s=r(5155),n=r(2115),a=r(9434),o=r(646),i=r(4861),l=r(5339),u=r(1284),c=r(4416);let d=(0,n.createContext)(void 0);function m(e){let{children:t}=e,[r,a]=(0,n.useState)([]),o=(0,n.useCallback)(e=>{let t=Math.random().toString(36).substr(2,9),r={...e,id:t};a(e=>[...e,r]),setTimeout(()=>{i(t)},e.duration||5e3)},[]),i=(0,n.useCallback)(e=>{a(t=>t.filter(t=>t.id!==e))},[]),l=(0,n.useCallback)((e,t)=>{o({type:"success",title:e,description:t})},[o]),u=(0,n.useCallback)((e,t)=>{o({type:"error",title:e,description:t})},[o]),c=(0,n.useCallback)((e,t)=>{o({type:"warning",title:e,description:t})},[o]),m=(0,n.useCallback)((e,t)=>{o({type:"info",title:e,description:t})},[o]);return(0,s.jsxs)(d.Provider,{value:{toasts:r,addToast:o,removeToast:i,success:l,error:u,warning:c,info:m},children:[t,(0,s.jsx)(g,{toasts:r,onRemove:i})]})}function g(e){let{toasts:t,onRemove:r}=e;return(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.map(e=>(0,s.jsx)(h,{toast:e,onRemove:r},e.id))})}function h(e){let{toast:t,onRemove:r}=e,n={success:o.A,error:i.A,warning:l.A,info:u.A}[t.type];return(0,s.jsx)("div",{className:(0,a.cn)("max-w-sm w-full border rounded-lg p-4 shadow-lg animate-slide-down",{success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"}[t.type]),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(n,{className:(0,a.cn)("h-5 w-5",{success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[t.type])})}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:t.title}),t.description&&(0,s.jsx)("p",{className:"mt-1 text-sm opacity-90",children:t.description}),t.action&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("button",{onClick:t.action.onClick,className:"text-sm font-medium underline hover:no-underline",children:t.action.label})})]}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsx)("button",{onClick:()=>r(t.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})})]})})}let f={success:(e,t)=>{console.log("Success:",e,t)},error:(e,t)=>{console.log("Error:",e,t)},warning:(e,t)=>{console.log("Warning:",e,t)},info:(e,t)=>{console.log("Info:",e,t)}};function p(){let e=(0,n.useContext)(d);return e?{success:e.success,error:e.error,warning:e.warning,info:e.info}:{success:f.success,error:f.error,warning:f.warning,info:f.info}}},7292:(e,t,r)=>{r.d(t,{f:()=>n,j:()=>a});class s{async signUp(e,t,r){await new Promise(e=>setTimeout(e,1e3));let s={id:Date.now().toString(),email:e,user_metadata:r||{}};return this.currentUser=s,this.notifyListeners(),{data:{user:s,session:{access_token:"mock-token"}},error:null}}async signInWithPassword(e,t){if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e&&"demo123"===t){let t={id:"1",email:e,user_metadata:{username:"演示用户"}};return this.currentUser=t,this.notifyListeners(),{data:{user:t,session:{access_token:"mock-token"}},error:null}}return{data:{user:null,session:null},error:{message:"邮箱或密码错误"}}}async signOut(){return this.currentUser=null,this.notifyListeners(),{error:null}}async resetPasswordForEmail(e){return await new Promise(e=>setTimeout(e,1e3)),{error:null}}async updateUser(e){return await new Promise(e=>setTimeout(e,1e3)),this.currentUser&&(this.currentUser={...this.currentUser,...e},this.notifyListeners()),{error:null}}async getSession(){return{data:{session:this.currentUser?{user:this.currentUser}:null}}}onAuthStateChange(e){let t=t=>{e(t?"SIGNED_IN":"SIGNED_OUT",t?{user:t}:null)};return this.listeners.push(t),{data:{subscription:{unsubscribe:()=>{let e=this.listeners.indexOf(t);e>-1&&this.listeners.splice(e,1)}}}}}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}constructor(){this.currentUser=null,this.listeners=[]}}let n=new s,a={auth:n}},9053:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(5155),n=r(2115),a=r(5695),o=r(283);function i(e){let{children:t,redirectTo:r="/auth/login"}=e,{user:i,loading:l}=(0,o.A)(),u=(0,a.useRouter)();return((0,n.useEffect)(()=>{l||i||u.push(r)},[i,l,u,r]),l)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):i?(0,s.jsx)(s.Fragment,{children:t}):null}},9148:(e,t,r)=>{r.d(t,{E:()=>s});class s{saveSession(e){try{let t=this.getAllSessions(),r=t.findIndex(t=>t.id===e.id);return r>=0?t[r]=e:t.push(e),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t)),this.updateStats(),!0}catch(e){return console.error("Failed to save interview session:",e),!1}}getAllSessions(e){try{let t=localStorage.getItem(this.STORAGE_KEY);if(!t)return[];let r=JSON.parse(t).map(e=>({...e,startTime:new Date(e.startTime),endTime:e.endTime?new Date(e.endTime):void 0,messages:e.messages.map(e=>({...e,timestamp:new Date(e.timestamp)}))}));if(e)return r.filter(t=>t.userId===e);return r}catch(e){return console.error("Failed to load interview sessions:",e),[]}}getSessionById(e){return this.getAllSessions().find(t=>t.id===e)||null}deleteSession(e){try{let t=this.getAllSessions().filter(t=>t.id!==e);return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t)),this.updateStats(),!0}catch(e){return console.error("Failed to delete interview session:",e),!1}}updateSessionNotes(e,t){try{let r=this.getAllSessions(),s=r.findIndex(t=>t.id===e);if(s>=0)return r[s].userNotes=t,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(r)),!0;return!1}catch(e){return console.error("Failed to update session notes:",e),!1}}updateSessionRating(e,t){try{let r=this.getAllSessions(),s=r.findIndex(t=>t.id===e);if(s>=0)return r[s].rating=Math.max(1,Math.min(5,t)),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(r)),this.updateStats(),!0;return!1}catch(e){return console.error("Failed to update session rating:",e),!1}}updateSessionTags(e,t){try{let r=this.getAllSessions(),s=r.findIndex(t=>t.id===e);if(s>=0)return r[s].tags=t,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(r)),!0;return!1}catch(e){return console.error("Failed to update session tags:",e),!1}}getFilteredSessions(e){let t=this.getAllSessions(e.userId);return e.company&&(t=t.filter(t=>t.company.toLowerCase().includes(e.company.toLowerCase()))),e.position&&(t=t.filter(t=>t.position.toLowerCase().includes(e.position.toLowerCase()))),e.interviewType&&(t=t.filter(t=>t.interviewType===e.interviewType)),e.status&&(t=t.filter(t=>t.status===e.status)),e.dateFrom&&(t=t.filter(t=>t.startTime>=e.dateFrom)),e.dateTo&&(t=t.filter(t=>t.startTime<=e.dateTo)),e.minRating&&(t=t.filter(t=>(t.rating||0)>=e.minRating)),e.tags&&e.tags.length>0&&(t=t.filter(t=>t.tags&&t.tags.some(t=>e.tags.includes(t)))),t.sort((e,t)=>t.startTime.getTime()-e.startTime.getTime())}getStats(e){try{let t=this.getAllSessions(e),r=t.filter(e=>"completed"===e.status);if(0===r.length)return{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]};let s=r.reduce((e,t)=>e+t.duration,0),n=r.filter(e=>{var t;return null==(t=e.summary)?void 0:t.overallScore}).reduce((e,t)=>{var r;return e+((null==(r=t.summary)?void 0:r.overallScore)||0)},0)/r.filter(e=>{var t;return null==(t=e.summary)?void 0:t.overallScore}).length||0,a=new Map;r.forEach(e=>{a.set(e.company,(a.get(e.company)||0)+1)});let o=Array.from(a.entries()).map(e=>{let[t,r]=e;return{company:t,count:r}}).sort((e,t)=>t.count-e.count).slice(0,5),i=new Map;r.forEach(e=>{i.set(e.position,(i.get(e.position)||0)+1)});let l=Array.from(i.entries()).map(e=>{let[t,r]=e;return{position:t,count:r}}).sort((e,t)=>t.count-e.count).slice(0,5),u=this.calculateMonthlyProgress(r),c=this.calculateSkillProgress(r);return{totalSessions:t.length,totalDuration:s,averageScore:Math.round(n),completionRate:Math.round(r.length/t.length*100),topCompanies:o,topPositions:l,monthlyProgress:u,skillProgress:c}}catch(e){return console.error("Failed to calculate stats:",e),{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]}}}calculateMonthlyProgress(e){let t=new Map;return e.forEach(e=>{var r;let s=e.startTime.toISOString().substring(0,7),n=t.get(s)||{sessions:0,totalScore:0,count:0};n.sessions++,(null==(r=e.summary)?void 0:r.overallScore)&&(n.totalScore+=e.summary.overallScore,n.count++),t.set(s,n)}),Array.from(t.entries()).map(e=>{let[t,r]=e;return{month:t,sessions:r.sessions,avgScore:r.count>0?Math.round(r.totalScore/r.count):0}}).sort((e,t)=>e.month.localeCompare(t.month)).slice(-6)}calculateSkillProgress(e){return["技术能力","沟通表达","逻辑思维","团队协作","学习能力"].map(t=>{let r=e.slice(-5).filter(e=>{var t;return null==(t=e.summary)?void 0:t.overallScore}).map(e=>e.summary.overallScore),s=r.length>0?Math.round(r.reduce((e,t)=>e+t,0)/r.length):0,n="stable";if(r.length>=3){let e=r.slice(0,Math.floor(r.length/2)),t=r.slice(Math.floor(r.length/2)),s=e.reduce((e,t)=>e+t,0)/e.length,a=t.reduce((e,t)=>e+t,0)/t.length;a>s+5?n="up":a<s-5&&(n="down")}return{skill:t,score:s,trend:n}})}updateStats(){}exportSessions(e){return JSON.stringify(this.getAllSessions(e),null,2)}importSessions(e){try{let t=JSON.parse(e),r=this.getAllSessions(),s=new Map;r.forEach(e=>{s.set(e.id,e)}),t.forEach(e=>{s.set(e.id,e)});let n=Array.from(s.values());return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(n)),this.updateStats(),!0}catch(e){return console.error("Failed to import sessions:",e),!1}}clearAllData(){try{return localStorage.removeItem(this.STORAGE_KEY),localStorage.removeItem(this.STATS_KEY),!0}catch(e){return console.error("Failed to clear data:",e),!1}}constructor(){this.STORAGE_KEY="jobplus_interview_sessions",this.STATS_KEY="jobplus_interview_stats"}}},9434:(e,t,r)=>{r.d(t,{cn:()=>a});var s=r(2596),n=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}}}]);