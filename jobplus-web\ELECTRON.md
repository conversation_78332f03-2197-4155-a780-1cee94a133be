# JobPlus Electron 桌面应用

JobPlus 现在支持作为桌面应用程序运行，使用 Electron 框架构建。

## 功能特性

- 🖥️ **跨平台支持**: Windows, macOS, Linux
- 🔒 **安全性**: 上下文隔离和预加载脚本
- 🎤 **音频权限**: 自动处理麦克风权限
- 🎨 **原生体验**: 系统主题检测和原生菜单
- 📁 **文件操作**: 支持文件选择和保存
- 🔄 **热重载**: 开发环境支持热重载

## 安装依赖

首先安装 Electron 相关依赖：

```bash
npm install --save-dev electron electron-builder concurrently wait-on cross-env
```

## 开发环境

### 启动开发环境
```bash
npm run electron-dev
```

这个命令会：
1. 启动 Next.js 开发服务器 (http://localhost:3000)
2. 等待服务器就绪
3. 启动 Electron 应用

### 仅启动 Electron (需要 Next.js 服务器已运行)
```bash
npm run electron
```

## 生产构建

### 构建静态文件
```bash
npm run build
npm run export
```

### 打包桌面应用
```bash
npm run dist
```

这会创建适合当前平台的安装包在 `dist/` 目录中。

### 仅打包不创建安装程序
```bash
npm run pack
```

## 项目结构

```
jobplus-web/
├── electron/
│   ├── main.js          # Electron 主进程
│   ├── preload.js       # 预加载脚本
│   └── dev-runner.js    # 开发环境启动器
├── src/
│   ├── hooks/
│   │   └── useElectron.ts  # Electron 检测 Hook
│   └── types/
│       └── electron.d.ts   # Electron 类型定义
├── public/
│   └── icon.png         # 应用图标
└── out/                 # Next.js 静态导出目录
```

## 配置说明

### package.json
- `main`: 指向 Electron 主进程文件
- `homepage`: 设置为 "./" 以支持静态文件
- `build`: Electron Builder 配置

### next.config.ts
- `output: 'export'`: 启用静态导出
- `trailingSlash: true`: 确保路由正确
- `images.unoptimized: true`: 禁用图片优化

## 安全特性

1. **上下文隔离**: 渲染进程无法直接访问 Node.js API
2. **预加载脚本**: 安全地暴露必要的 API
3. **外链处理**: 自动在系统浏览器中打开外部链接
4. **导航保护**: 防止导航到恶意网站

## API 接口

通过 `window.electronAPI` 访问：

```typescript
// 获取应用版本
const version = await window.electronAPI.getVersion();

// 显示消息框
await window.electronAPI.showMessageBox({
  type: 'info',
  title: '提示',
  message: '这是一个消息'
});

// 请求音频权限
const hasPermission = await window.electronAPI.requestMediaPermissions();

// 检测主题
const theme = window.electronAPI.getTheme();

// 监听主题变化
const unsubscribe = window.electronAPI.onThemeChange((theme) => {
  console.log('主题变化:', theme);
});
```

## React Hook 使用

```typescript
import { useElectron, useElectronAPI } from '@/hooks/useElectron';

function MyComponent() {
  const { isElectron, platform, version } = useElectron();
  const electronAPI = useElectronAPI();
  
  if (isElectron) {
    return <div>运行在 Electron {version} 中</div>;
  }
  
  return <div>运行在浏览器中</div>;
}
```

## 故障排除

### 开发环境问题
1. 确保端口 3000 未被占用
2. 检查 Node.js 版本兼容性
3. 清除 node_modules 并重新安装

### 构建问题
1. 确保 `out/` 目录存在且包含静态文件
2. 检查 Next.js 导出是否成功
3. 验证 Electron Builder 配置

### 权限问题
- macOS: 可能需要在系统偏好设置中授予麦克风权限
- Windows: 确保应用有音频设备访问权限

## 发布

1. 更新 `package.json` 中的版本号
2. 运行 `npm run dist` 创建安装包
3. 测试生成的安装包
4. 发布到应用商店或分发平台
