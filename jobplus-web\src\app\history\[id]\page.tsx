"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InterviewStorageService, InterviewSession, InterviewMessage } from "@/lib/interview-storage";
import { 
  Brain, 
  Calendar,
  Clock,
  Building,
  Briefcase,
  Star,
  ArrowLeft,
  User,
  Bot,
  MessageSquare,
  Edit,
  Save,
  X,
  Download,
  Share,
  Copy,
  Award,
  TrendingUp,
  Lightbulb,
  Target,
  CheckCircle
} from "lucide-react";
import { PageTransition, FadeIn } from "@/components/ui/page-transition";
import { useToastActions } from "@/components/ui/toast";

export default function InterviewDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const toast = useToastActions();
  const [session, setSession] = useState<InterviewSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingNotes, setEditingNotes] = useState(false);
  const [notes, setNotes] = useState("");
  const [rating, setRating] = useState(0);

  const storageService = new InterviewStorageService();

  useEffect(() => {
    loadSession();
  }, [params.id]);

  const loadSession = () => {
    setLoading(true);
    try {
      const sessionData = storageService.getSessionById(params.id as string);
      if (sessionData) {
        setSession(sessionData);
        setNotes(sessionData.userNotes || "");
        setRating(sessionData.rating || 0);
      } else {
        router.push('/history');
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      router.push('/history');
    } finally {
      setLoading(false);
    }
  };

  const saveNotes = () => {
    if (session) {
      const success = storageService.updateSessionNotes(session.id, notes);
      if (success) {
        setSession({ ...session, userNotes: notes });
        setEditingNotes(false);
      } else {
        alert('保存失败，请重试');
      }
    }
  };

  const saveRating = (newRating: number) => {
    if (session) {
      const success = storageService.updateSessionRating(session.id, newRating);
      if (success) {
        setSession({ ...session, rating: newRating });
        setRating(newRating);
      }
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'active': return '进行中';
      case 'paused': return '已暂停';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'technical': return '技术面试';
      case 'behavioral': return '行为面试';
      case 'mixed': return '综合面试';
      default: return type;
    }
  };

  const exportSession = () => {
    if (session) {
      const data = JSON.stringify(session, null, 2);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `interview-${session.company}-${session.position}-${session.startTime.toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('已复制到剪贴板');
    });
  };

  const renderMessage = (message: InterviewMessage, index: number) => (
    <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
        message.type === 'user' 
          ? 'bg-blue-600 text-white' 
          : message.type === 'ai'
          ? 'bg-green-100 text-green-900'
          : 'bg-gray-100 text-gray-900'
      }`}>
        <div className="flex items-center space-x-2 mb-2">
          {message.type === 'user' ? (
            <User className="h-4 w-4" />
          ) : message.type === 'ai' ? (
            <Bot className="h-4 w-4" />
          ) : (
            <MessageSquare className="h-4 w-4" />
          )}
          <span className="text-xs opacity-75">
            {message.timestamp.toLocaleTimeString()}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyToClipboard(message.content)}
            className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
        {message.suggestions && message.suggestions.length > 0 && (
          <div className="mt-2 space-y-1">
            {message.suggestions.map((suggestion, idx) => (
              <div key={idx} className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
                💡 {suggestion}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderStarRating = (currentRating: number, onRate: (rating: number) => void) => (
    <div className="flex items-center space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          onClick={() => onRate(star)}
          className={`p-1 rounded ${
            star <= currentRating ? 'text-yellow-500' : 'text-gray-300'
          } hover:text-yellow-400 transition-colors`}
        >
          <Star className="h-5 w-5 fill-current" />
        </button>
      ))}
    </div>
  );

  if (loading) {
    return (
      <ProtectedRoute>
        <PageTransition>
          <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载面试详情中...</p>
          </div>
        </div>
        </PageTransition>
      </ProtectedRoute>
    );
  }

  if (!session) {
    return (
      <ProtectedRoute>
        <PageTransition>
          <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">面试记录不存在</h2>
            <p className="text-gray-600 mb-4">请检查链接是否正确</p>
            <Link href="/history">
              <Button>返回面试记录</Button>
            </Link>
          </div>
        </div>
        </PageTransition>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <PageTransition>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Header */}
        <header className="bg-white border-b">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/history">
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <Brain className="h-8 w-8 text-blue-600" />
                <span className="text-2xl font-bold text-gray-900">JobPlus</span>
              </div>
            </div>
            <nav className="flex items-center space-x-4">
              <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">
                控制台
              </Link>
              <Link href="/history" className="text-gray-600 hover:text-gray-900">
                面试记录
              </Link>
              <span className="text-blue-600 font-medium">详情</span>
            </nav>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          {/* Session Header */}
          <div className="mb-8">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {session.company} - {session.position}
                </h1>
                <div className="flex items-center space-x-4 text-gray-600">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(session.status)}`}>
                    {getStatusText(session.status)}
                  </span>
                  <span>{getTypeText(session.interviewType)}</span>
                  <span>{formatDate(session.startTime)}</span>
                  <span>{formatDuration(session.duration)}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button onClick={exportSession} variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700">评分：</span>
              {renderStarRating(rating, saveRating)}
              <span className="text-sm text-gray-600">
                {rating > 0 ? `${rating}/5 星` : '未评分'}
              </span>
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Summary */}
              {session.summary && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Award className="h-5 w-5" />
                      <span>面试总结</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6 mb-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600 mb-1">
                          {session.summary.overallScore}
                        </div>
                        <div className="text-sm text-gray-600">综合评分</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600 mb-1">
                          {session.summary.totalResponses}
                        </div>
                        <div className="text-sm text-gray-600">回答次数</div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {session.summary.strongPoints.length > 0 && (
                        <div>
                          <h4 className="font-medium text-green-900 mb-2 flex items-center">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            表现优势
                          </h4>
                          <ul className="text-sm text-green-800 space-y-1">
                            {session.summary.strongPoints.map((point, index) => (
                              <li key={index}>• {point}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {session.summary.improvementAreas.length > 0 && (
                        <div>
                          <h4 className="font-medium text-orange-900 mb-2 flex items-center">
                            <Target className="h-4 w-4 mr-2" />
                            改进建议
                          </h4>
                          <ul className="text-sm text-orange-800 space-y-1">
                            {session.summary.improvementAreas.map((area, index) => (
                              <li key={index}>• {area}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {session.summary.keyInsights && session.summary.keyInsights.length > 0 && (
                        <div>
                          <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                            <Lightbulb className="h-4 w-4 mr-2" />
                            关键洞察
                          </h4>
                          <ul className="text-sm text-blue-800 space-y-1">
                            {session.summary.keyInsights.map((insight, index) => (
                              <li key={index}>• {insight}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Conversation */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MessageSquare className="h-5 w-5" />
                    <span>对话记录</span>
                  </CardTitle>
                  <CardDescription>
                    完整的面试对话历史，包括您的回答和AI建议
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="max-h-96 overflow-y-auto">
                    {session.messages.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">暂无对话记录</p>
                    ) : (
                      session.messages.map((message, index) => renderMessage(message, index))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Notes */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Edit className="h-5 w-5" />
                      <span>个人备注</span>
                    </div>
                    {!editingNotes && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingNotes(true)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {editingNotes ? (
                    <div className="space-y-3">
                      <textarea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="添加您的备注..."
                        className="w-full p-3 border rounded-md resize-none"
                        rows={4}
                      />
                      <div className="flex space-x-2">
                        <Button onClick={saveNotes} size="sm">
                          <Save className="h-4 w-4 mr-2" />
                          保存
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditingNotes(false);
                            setNotes(session.userNotes || "");
                          }}
                        >
                          <X className="h-4 w-4 mr-2" />
                          取消
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      {notes ? (
                        <p className="text-sm text-gray-700 whitespace-pre-wrap">{notes}</p>
                      ) : (
                        <p className="text-sm text-gray-500 italic">暂无备注</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Session Info */}
              <Card>
                <CardHeader>
                  <CardTitle>面试信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2 text-sm">
                    <Building className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">公司：</span>
                    <span>{session.company}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Briefcase className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">职位：</span>
                    <span>{session.position}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">开始时间：</span>
                    <span>{formatDate(session.startTime)}</span>
                  </div>
                  {session.endTime && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">结束时间：</span>
                      <span>{formatDate(session.endTime)}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2 text-sm">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">时长：</span>
                    <span>{formatDuration(session.duration)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
      </PageTransition>
    </ProtectedRoute>
  );
}
