// Audio processing utilities for speech recognition and audio analysis

export interface AudioConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  echoCancellation: boolean;
  noiseSuppression: boolean;
}

export interface SpeechRecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
  timestamp: Date;
}

export interface AudioAnalysis {
  volume: number;
  frequency: number;
  speechDetected: boolean;
  silenceDuration: number;
}

export class AudioProcessor {
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private dataArray: Uint8Array | null = null;
  private stream: MediaStream | null = null;
  private isRecording = false;
  private audioChunks: Blob[] = [];
  private onDataCallback?: (data: AudioAnalysis) => void;
  private onSpeechCallback?: (result: SpeechRecognitionResult) => void;

  constructor(
    private config: AudioConfig = {
      sampleRate: 44100,
      channels: 1,
      bitDepth: 16,
      echoCancellation: true,
      noiseSuppression: true,
    }
  ) {}

  async initialize(): Promise<boolean> {
    try {
      // Request microphone access
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: this.config.echoCancellation,
          noiseSuppression: this.config.noiseSuppression,
        },
      });

      // Create audio context for analysis
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.microphone = this.audioContext.createMediaStreamSource(this.stream);
      
      this.analyser.fftSize = 256;
      this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
      
      this.microphone.connect(this.analyser);

      // Create media recorder for audio capture
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: this.getSupportedMimeType(),
      });

      this.setupMediaRecorderEvents();
      
      return true;
    } catch (error) {
      console.error('Failed to initialize audio processor:', error);
      return false;
    }
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav',
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // fallback
  }

  private setupMediaRecorderEvents(): void {
    if (!this.mediaRecorder) return;

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data);
      }
    };

    this.mediaRecorder.onstop = () => {
      const audioBlob = new Blob(this.audioChunks, { 
        type: this.mediaRecorder?.mimeType || 'audio/webm' 
      });
      this.processAudioBlob(audioBlob);
      this.audioChunks = [];
    };
  }

  startRecording(): boolean {
    if (!this.mediaRecorder || this.isRecording) {
      return false;
    }

    try {
      this.audioChunks = [];
      this.mediaRecorder.start(1000); // Collect data every second
      this.isRecording = true;
      this.startAudioAnalysis();
      return true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      return false;
    }
  }

  stopRecording(): boolean {
    if (!this.mediaRecorder || !this.isRecording) {
      return false;
    }

    try {
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.stopAudioAnalysis();
      return true;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      return false;
    }
  }

  private startAudioAnalysis(): void {
    if (!this.analyser || !this.dataArray) return;

    const analyze = () => {
      if (!this.isRecording) return;

      this.analyser!.getByteFrequencyData(this.dataArray!);
      
      // Calculate volume (RMS)
      let sum = 0;
      for (let i = 0; i < this.dataArray!.length; i++) {
        sum += this.dataArray![i] * this.dataArray![i];
      }
      const volume = Math.sqrt(sum / this.dataArray!.length) / 255;

      // Detect speech (simple threshold-based)
      const speechThreshold = 0.1;
      const speechDetected = volume > speechThreshold;

      // Calculate dominant frequency
      let maxIndex = 0;
      let maxValue = 0;
      for (let i = 0; i < this.dataArray!.length; i++) {
        if (this.dataArray![i] > maxValue) {
          maxValue = this.dataArray![i];
          maxIndex = i;
        }
      }
      const frequency = (maxIndex * this.config.sampleRate) / (this.analyser!.fftSize * 2);

      const analysis: AudioAnalysis = {
        volume: volume * 100,
        frequency,
        speechDetected,
        silenceDuration: speechDetected ? 0 : Date.now(), // Simplified
      };

      if (this.onDataCallback) {
        this.onDataCallback(analysis);
      }

      requestAnimationFrame(analyze);
    };

    analyze();
  }

  private stopAudioAnalysis(): void {
    // Analysis will stop when isRecording becomes false
  }

  private async processAudioBlob(audioBlob: Blob): Promise<void> {
    try {
      // In a real implementation, this would send the audio to a speech recognition service
      // For demo purposes, we'll simulate speech recognition
      const mockSpeechRecognition = await this.mockSpeechRecognition(audioBlob);
      
      if (this.onSpeechCallback && mockSpeechRecognition) {
        this.onSpeechCallback(mockSpeechRecognition);
      }
    } catch (error) {
      console.error('Failed to process audio blob:', error);
    }
  }

  private async mockSpeechRecognition(audioBlob: Blob): Promise<SpeechRecognitionResult | null> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Mock responses based on audio duration
    const duration = audioBlob.size / 1000; // Rough estimate
    
    if (duration < 1) {
      return null; // Too short to be meaningful speech
    }

    const mockTexts = [
      "我有三年的前端开发经验，主要使用React和Vue.js框架。",
      "我最大的优势是学习能力强，能够快速适应新技术和新环境。",
      "我希望在贵公司能够承担更多的技术挑战，提升自己的技术水平。",
      "我对这个职位很感兴趣，因为它符合我的职业规划和发展方向。",
      "我在之前的项目中负责了整个前端架构的设计和实现。",
      "我认为团队协作非常重要，我善于与不同背景的同事沟通合作。",
      "我会持续关注行业动态，学习新的技术和最佳实践。",
      "我希望能够在技术和管理方面都有所发展。"
    ];

    const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)];
    const confidence = 0.8 + Math.random() * 0.2; // 80-100% confidence

    return {
      text: randomText,
      confidence,
      isFinal: true,
      timestamp: new Date(),
    };
  }

  setOnDataCallback(callback: (data: AudioAnalysis) => void): void {
    this.onDataCallback = callback;
  }

  setOnSpeechCallback(callback: (result: SpeechRecognitionResult) => void): void {
    this.onSpeechCallback = callback;
  }

  getRecordingState(): boolean {
    return this.isRecording;
  }

  async cleanup(): Promise<void> {
    if (this.isRecording) {
      this.stopRecording();
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    if (this.audioContext) {
      await this.audioContext.close();
      this.audioContext = null;
    }

    this.mediaRecorder = null;
    this.analyser = null;
    this.microphone = null;
    this.dataArray = null;
  }

  // Static method to check browser support
  static isSupported(): boolean {
    return !!(
      typeof navigator !== 'undefined' &&
      navigator.mediaDevices &&
      typeof navigator.mediaDevices.getUserMedia === 'function' &&
      typeof window !== 'undefined' &&
      window.MediaRecorder &&
      (window.AudioContext || (window as any).webkitAudioContext)
    );
  }

  // Static method to request permissions
  static async requestPermissions(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      return false;
    }
  }
}
