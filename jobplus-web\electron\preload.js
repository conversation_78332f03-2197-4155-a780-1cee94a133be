const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // Platform info
  platform: process.platform,
  
  // Environment
  isDev: process.env.NODE_ENV === 'development',
  
  // Audio/Media permissions (for interview recording)
  requestMediaPermissions: async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: true, 
        video: false 
      });
      // Close the stream immediately, we just wanted to check permissions
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Media permission denied:', error);
      return false;
    }
  },
  
  // File system operations (if needed)
  selectFile: () => ipcRenderer.invoke('select-file'),
  saveFile: (data, filename) => ipc<PERSON>enderer.invoke('save-file', data, filename),
  
  // Window controls
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close'),
  toggleFullscreen: () => ipcRenderer.invoke('window-toggle-fullscreen'),
  isFullscreen: () => ipcRenderer.invoke('window-is-fullscreen'),
  
  // Theme detection
  getTheme: () => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  },
  
  // Listen for theme changes
  onThemeChange: (callback) => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', (e) => {
      callback(e.matches ? 'dark' : 'light');
    });
    return () => mediaQuery.removeEventListener('change', callback);
  }
});

// Security: Remove Node.js globals from renderer process
delete window.require;
delete window.exports;
delete window.module;

// Add some useful globals for the app
window.isElectron = true;
window.electronVersion = process.versions.electron;
window.nodeVersion = process.versions.node;
window.chromeVersion = process.versions.chrome;
