"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AudioProcessor, AudioAnalysis, SpeechRecognitionResult } from "@/lib/audio-processor";
import { AIAssistant, InterviewContext, AIResponse } from "@/lib/ai-assistant";
import { InterviewStorageService, InterviewSession as StoredSession, InterviewMessage } from "@/lib/interview-storage";
import {
  Brain,
  Mic,
  MicOff,
  Play,
  Pause,
  Square,
  Send,
  ArrowLeft,
  Settings,
  Volume2,
  VolumeX,
  MessageSquare,
  Lightbulb,
  Clock,
  User,
  <PERSON><PERSON>,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  Save,
  History,
  Video,
  LogOut
} from "lucide-react";
import { PageTransition, FadeIn } from "@/components/ui/page-transition";
import { useToastActions } from "@/components/ui/toast";
import { mockAuth } from "@/lib/mock-auth";

interface Message {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  isQuestion?: boolean;
  suggestions?: string[];
}

interface InterviewSession {
  id: string;
  company: string;
  position: string;
  interviewType: 'technical' | 'behavioral' | 'mixed';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  startTime: Date;
  status: 'preparing' | 'active' | 'paused' | 'completed';
}

export default function InterviewPage() {
  const { user } = useAuth();
  const toast = useToastActions();
  const [isRecording, setIsRecording] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [currentSession, setCurrentSession] = useState<InterviewSession | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [sessionDuration, setSessionDuration] = useState(0);
  const [audioSupported, setAudioSupported] = useState(true);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [currentAudioAnalysis, setCurrentAudioAnalysis] = useState<AudioAnalysis | null>(null);

  // Setup form state
  const [setupForm, setSetupForm] = useState({
    company: "",
    position: "",
    interviewType: "technical" as "technical" | "behavioral" | "mixed",
    difficulty: "intermediate" as "beginner" | "intermediate" | "advanced"
  });

  const audioProcessorRef = useRef<AudioProcessor | null>(null);
  const aiAssistantRef = useRef<AIAssistant | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const storageServiceRef = useRef<InterviewStorageService>(new InterviewStorageService());

  // Check audio support and permissions on mount
  useEffect(() => {
    const checkAudioSupport = async () => {
      const supported = AudioProcessor.isSupported();
      setAudioSupported(supported);

      if (supported) {
        const hasPermission = await AudioProcessor.requestPermissions();
        setPermissionGranted(hasPermission);
      }
    };

    checkAudioSupport();
  }, []);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Session duration timer
  useEffect(() => {
    if (currentSession?.status === 'active') {
      durationIntervalRef.current = setInterval(() => {
        setSessionDuration(prev => prev + 1);
      }, 1000);
    } else {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    }

    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, [currentSession?.status]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioProcessorRef.current) {
        audioProcessorRef.current.cleanup();
      }
    };
  }, []);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startInterview = async () => {
    if (!setupForm.company || !setupForm.position) {
      toast.warning("请填写完整信息", "请填写公司名称和职位");
      return;
    }

    if (!audioSupported) {
      toast.error("浏览器不支持", "您的浏览器不支持音频录制功能");
      return;
    }

    if (!permissionGranted) {
      const hasPermission = await AudioProcessor.requestPermissions();
      if (!hasPermission) {
        toast.error("权限不足", "需要麦克风权限才能使用语音功能");
        return;
      }
      setPermissionGranted(true);
      toast.success("权限获取成功", "麦克风权限已获取");
    }

    // Initialize audio processor
    audioProcessorRef.current = new AudioProcessor();
    const initialized = await audioProcessorRef.current.initialize();

    if (!initialized) {
      toast.error("初始化失败", "音频初始化失败，请检查麦克风设置");
      return;
    }

    // Setup audio callbacks
    audioProcessorRef.current.setOnDataCallback((analysis: AudioAnalysis) => {
      setCurrentAudioAnalysis(analysis);
      setAudioLevel(analysis.volume);
    });

    audioProcessorRef.current.setOnSpeechCallback(async (result: SpeechRecognitionResult) => {
      if (result.isFinal && result.text.trim()) {
        // Add user message
        const userMessage: Message = {
          id: Date.now().toString(),
          type: 'user',
          content: result.text,
          timestamp: result.timestamp
        };

        setMessages(prev => [...prev, userMessage]);

        // Get AI response
        if (aiAssistantRef.current) {
          setIsProcessing(true);
          try {
            const aiResponse = await aiAssistantRef.current.analyzeResponse(result.text);

            const aiMessage: Message = {
              id: (Date.now() + 1).toString(),
              type: 'ai',
              content: aiResponse.content,
              timestamp: new Date(),
              suggestions: aiResponse.suggestions
            };

            setMessages(prev => [...prev, aiMessage]);
          } catch (error) {
            console.error('AI analysis failed:', error);
          } finally {
            setIsProcessing(false);
          }
        }
      }
    });

    // Initialize AI assistant
    const interviewContext: InterviewContext = {
      company: setupForm.company,
      position: setupForm.position,
      interviewType: setupForm.interviewType,
      difficulty: setupForm.difficulty
    };

    aiAssistantRef.current = new AIAssistant(interviewContext);

    const session: InterviewSession = {
      id: Date.now().toString(),
      company: setupForm.company,
      position: setupForm.position,
      interviewType: setupForm.interviewType,
      difficulty: setupForm.difficulty,
      startTime: new Date(),
      status: 'active'
    };

    setCurrentSession(session);
    setSessionDuration(0);

    // Add welcome message
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      type: 'ai',
      content: `欢迎来到${setupForm.company}的${setupForm.position}面试！我是您的AI面试助手，将为您提供实时建议和支持。请开始您的面试，我会在适当的时候为您提供帮助。`,
      timestamp: new Date()
    };

    setMessages([welcomeMessage]);
  };

  const endInterview = async () => {
    if (currentSession && user) {
      const completedSession = { ...currentSession, status: 'completed' as const };
      setCurrentSession(completedSession);
      stopRecording();

      // Get conversation summary from AI assistant
      let summaryContent = `面试结束。总时长：${formatDuration(sessionDuration)}。感谢您使用JobPlus面试助手！`;
      let aiSummary = null;

      if (aiAssistantRef.current) {
        try {
          const rawSummary = aiAssistantRef.current.getConversationSummary();
          // Convert to InterviewSummary format
          aiSummary = {
            ...rawSummary,
            keyInsights: rawSummary.strongPoints.length > 0 ? rawSummary.strongPoints : ['面试表现良好'],
            recommendedActions: rawSummary.improvementAreas.length > 0 ? rawSummary.improvementAreas : ['继续保持']
          };

          summaryContent += `\n\n📊 面试总结：\n• 回答次数：${aiSummary.totalResponses}\n• 平均回答长度：${aiSummary.averageResponseLength}字\n• 综合评分：${aiSummary.overallScore}/100`;

          if (aiSummary.strongPoints.length > 0) {
            summaryContent += `\n• 优势：${aiSummary.strongPoints.join('、')}`;
          }

          if (aiSummary.improvementAreas.length > 0) {
            summaryContent += `\n• 改进建议：${aiSummary.improvementAreas.join('、')}`;
          }
        } catch (error) {
          console.error('Failed to generate summary:', error);
        }
      }

      const endMessage: Message = {
        id: Date.now().toString(),
        type: 'system',
        content: summaryContent,
        timestamp: new Date()
      };

      const finalMessages = [...messages, endMessage];
      setMessages(finalMessages);

      // Save interview session to storage
      try {
        const storedSession: StoredSession = {
          id: currentSession.id,
          userId: user.id,
          company: currentSession.company,
          position: currentSession.position,
          interviewType: currentSession.interviewType,
          difficulty: currentSession.difficulty,
          startTime: currentSession.startTime,
          endTime: new Date(),
          duration: sessionDuration,
          status: 'completed',
          messages: finalMessages.map(msg => ({
            id: msg.id,
            type: msg.type as 'user' | 'ai' | 'system' | 'interviewer',
            content: msg.content,
            timestamp: msg.timestamp,
            isQuestion: msg.isQuestion,
            suggestions: msg.suggestions
          })),
          summary: aiSummary || undefined
        };

        const saved = storageServiceRef.current.saveSession(storedSession);
        if (saved) {
          console.log('Interview session saved successfully');
        } else {
          console.error('Failed to save interview session');
        }
      } catch (error) {
        console.error('Error saving interview session:', error);
      }

      // Cleanup audio processor
      if (audioProcessorRef.current) {
        await audioProcessorRef.current.cleanup();
        audioProcessorRef.current = null;
      }
    }
  };

  const startRecording = async () => {
    if (!audioProcessorRef.current) {
      toast.error('系统错误', '音频处理器未初始化');
      return;
    }

    const success = audioProcessorRef.current.startRecording();
    if (success) {
      setIsRecording(true);
      setIsListening(true);
      toast.success('录音开始', '正在监听您的语音...');
    } else {
      toast.error('录音失败', '无法开始录音，请检查麦克风权限');
    }
  };

  const stopRecording = () => {
    if (audioProcessorRef.current && isRecording) {
      audioProcessorRef.current.stopRecording();
      setIsRecording(false);
      setIsListening(false);
      setAudioLevel(0);
      setCurrentAudioAnalysis(null);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");

    // Get AI response
    if (aiAssistantRef.current) {
      setIsProcessing(true);
      try {
        const aiResponse = await aiAssistantRef.current.analyzeResponse(inputMessage);

        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: aiResponse.content,
          timestamp: new Date(),
          suggestions: aiResponse.suggestions
        };

        setMessages(prev => [...prev, aiMessage]);
      } catch (error) {
        console.error('AI analysis failed:', error);

        // Fallback response
        const fallbackMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: "收到您的消息，我正在分析中。请继续您的面试。",
          timestamp: new Date()
        };

        setMessages(prev => [...prev, fallbackMessage]);
      } finally {
        setIsProcessing(false);
      }
    }
  };



  if (!currentSession) {
    // Interview Setup Screen
    return (
      <ProtectedRoute>
        <PageTransition>
          <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
          {/* Header */}
          <FadeIn direction="down">
            <header className="bg-white/80 backdrop-blur-sm border-b shadow-sm">
              <div className="container mx-auto px-4 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Brain className="h-8 w-8 text-blue-600" />
                  <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    JobPlus
                  </span>
                </div>
                <nav className="flex items-center space-x-4">
                  <Link href="/dashboard" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                    控制台
                  </Link>
                  <Link href="/resume" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                    简历中心
                  </Link>
                  <Link href="/interview" className="text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50">
                    面试室
                  </Link>
                  <Link href="/history" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                    面试记录
                  </Link>
                  <Button variant="ghost" size="icon" onClick={() => mockAuth.signOut()} className="hover:bg-red-50 hover:text-red-600">
                    <LogOut className="h-4 w-4" />
                  </Button>
                </nav>
              </div>
            </header>
          </FadeIn>

          <div className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto">
              <FadeIn delay={200}>
                <div className="mb-8 text-center">
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">
                    AI面试助手
                  </h1>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    设置您的面试信息，AI助手将为您提供实时建议和指导
                  </p>
                </div>
              </FadeIn>

              <FadeIn delay={400}>
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg">
                    <CardTitle className="text-2xl text-gray-800 flex items-center justify-center space-x-2">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                        <Video className="h-6 w-6 text-white" />
                      </div>
                      <span>开始面试</span>
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      设置您的面试信息，AI助手将为您提供实时建议
                    </CardDescription>
                  </CardHeader>
                <CardContent className="space-y-6 p-8">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-gray-700 font-medium">公司名称</Label>
                      <Input
                        id="company"
                        value={setupForm.company}
                        onChange={(e) => setSetupForm(prev => ({ ...prev, company: e.target.value }))}
                        placeholder="请输入公司名称"
                        className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="position" className="text-gray-700 font-medium">应聘职位</Label>
                      <Input
                        id="position"
                        value={setupForm.position}
                        onChange={(e) => setSetupForm(prev => ({ ...prev, position: e.target.value }))}
                        placeholder="请输入应聘职位"
                        className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-gray-700 font-medium">面试类型</Label>
                      <select
                        className="w-full p-3 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-20 transition-all"
                        value={setupForm.interviewType}
                        onChange={(e) => setSetupForm(prev => ({ ...prev, interviewType: e.target.value as any }))}
                      >
                        <option value="technical">技术面试</option>
                        <option value="behavioral">行为面试</option>
                        <option value="mixed">综合面试</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-700 font-medium">难度级别</Label>
                      <select
                        className="w-full p-3 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-20 transition-all"
                        value={setupForm.difficulty}
                        onChange={(e) => setSetupForm(prev => ({ ...prev, difficulty: e.target.value as any }))}
                      >
                        <option value="beginner">初级</option>
                        <option value="intermediate">中级</option>
                        <option value="advanced">高级</option>
                      </select>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-3 flex items-center space-x-2">
                      <Brain className="h-5 w-5" />
                      <span>AI助手功能</span>
                    </h4>
                    <ul className="text-sm text-blue-800 space-y-2">
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>实时语音识别和转录</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>智能回答建议和优化</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>面试技巧和要点提醒</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>表现评估和改进建议</span>
                      </li>
                    </ul>
                  </div>

                  {/* Audio Support Status */}
                  <div className={`p-4 rounded-lg border ${audioSupported && permissionGranted ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
                    <div className="flex items-center space-x-3">
                      {audioSupported && permissionGranted ? (
                        <>
                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-4 w-4 text-white" />
                          </div>
                          <span className="text-sm font-medium text-green-800">音频功能已就绪</span>
                        </>
                      ) : (
                        <>
                          <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <AlertCircle className="h-4 w-4 text-white" />
                          </div>
                          <span className="text-sm font-medium text-yellow-800">
                            {!audioSupported ? '浏览器不支持音频录制' : '需要麦克风权限'}
                          </span>
                        </>
                      )}
                    </div>
                  </div>

                  <Button onClick={startInterview} className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg" size="lg">
                    <Play className="h-5 w-5 mr-2" />
                    开始面试
                  </Button>
                </CardContent>
              </Card>
              </FadeIn>
            </div>
          </div>
        </div>
        </PageTransition>
      </ProtectedRoute>
    );
  }

  // Active Interview Screen
  return (
    <ProtectedRoute>
      <PageTransition>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex flex-col">
        {/* Header */}
        <header className="bg-white border-b flex-shrink-0">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Brain className="h-6 w-6 text-blue-600" />
                  <span className="text-xl font-bold text-gray-900">JobPlus</span>
                </div>
                <div className="text-sm text-gray-600">
                  {currentSession.company} - {currentSession.position}
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-sm">
                  <Clock className="h-4 w-4" />
                  <span>{formatDuration(sessionDuration)}</span>
                </div>
                <Link href="/history">
                  <Button variant="outline" size="sm">
                    <History className="h-4 w-4 mr-2" />
                    历史记录
                  </Button>
                </Link>
                <Button onClick={endInterview} variant="destructive" size="sm">
                  <Square className="h-4 w-4 mr-2" />
                  结束面试
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Messages Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.type === 'user' 
                      ? 'bg-blue-600 text-white' 
                      : message.type === 'ai'
                      ? 'bg-green-100 text-green-900'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <div className="flex items-center space-x-2 mb-1">
                      {message.type === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : message.type === 'ai' ? (
                        <Bot className="h-4 w-4" />
                      ) : (
                        <MessageSquare className="h-4 w-4" />
                      )}
                      <span className="text-xs opacity-75">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-sm">{message.content}</p>
                    {message.suggestions && (
                      <div className="mt-2 space-y-1">
                        {message.suggestions.map((suggestion, index) => (
                          <div key={index} className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
                            💡 {suggestion}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isProcessing && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-sm">AI正在分析...</span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="border-t bg-white p-4">
              <div className="flex space-x-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="输入您的问题或需要帮助的内容..."
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  className="flex-1"
                />
                <Button onClick={sendMessage} disabled={!inputMessage.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Audio Control Panel */}
          <div className="w-80 border-l bg-white p-4 space-y-4">
            <h3 className="font-medium text-gray-900">语音控制</h3>
            
            {/* Recording Controls */}
            <div className="space-y-3">
              <Button
                onClick={isRecording ? stopRecording : startRecording}
                className={`w-full ${isRecording ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700'}`}
                size="lg"
              >
                {isRecording ? (
                  <>
                    <MicOff className="h-5 w-5 mr-2" />
                    停止录音
                  </>
                ) : (
                  <>
                    <Mic className="h-5 w-5 mr-2" />
                    开始录音
                  </>
                )}
              </Button>

              {/* Audio Level Indicator */}
              {isListening && (
                <div className="space-y-2">
                  <div className="text-sm text-gray-600 flex items-center justify-between">
                    <span>音频级别</span>
                    {currentAudioAnalysis?.speechDetected && (
                      <span className="text-xs text-green-600 flex items-center">
                        <Volume2 className="h-3 w-3 mr-1" />
                        检测到语音
                      </span>
                    )}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-100 ${
                        currentAudioAnalysis?.speechDetected ? 'bg-green-600' : 'bg-blue-600'
                      }`}
                      style={{ width: `${audioLevel}%` }}
                    ></div>
                  </div>
                  {currentAudioAnalysis && (
                    <div className="text-xs text-gray-500">
                      音量: {Math.round(audioLevel)}% | 频率: {Math.round(currentAudioAnalysis.frequency)}Hz
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Quick Tips */}
            <div className="bg-yellow-50 p-3 rounded-lg">
              <h4 className="font-medium text-yellow-900 mb-2 flex items-center">
                <Lightbulb className="h-4 w-4 mr-2" />
                面试技巧
              </h4>
              <ul className="text-xs text-yellow-800 space-y-1">
                <li>• 保持眼神交流</li>
                <li>• 回答要具体有例子</li>
                <li>• 展示学习能力</li>
                <li>• 提问显示兴趣</li>
              </ul>
            </div>

            {/* Session Stats */}
            <div className="bg-blue-50 p-3 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">本次面试</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <div>时长: {formatDuration(sessionDuration)}</div>
                <div>消息: {messages.length}</div>
                <div>状态: {currentSession.status === 'active' ? '进行中' : '已暂停'}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </PageTransition>
    </ProtectedRoute>
  );
}
