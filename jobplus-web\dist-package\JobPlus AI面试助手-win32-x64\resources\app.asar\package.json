{"name": "jobplus-web", "version": "1.0.0", "description": "JobPlus AI面试助手 - 智能面试辅导桌面应用", "author": "JobPlus Team", "private": true, "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "next dev --turbopack", "build": "next build", "build-electron": "npm run build && electron-builder", "build-static": "next build && next export", "start": "next start", "lint": "next lint", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.53.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "cross-env": "^10.0.0", "electron": "13.1.7", "electron-builder": "^26.0.12", "electron-packager": "^17.1.2", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5", "wait-on": "^8.0.4"}, "build": {"appId": "com.jobplus.app", "productName": "JobPlus AI面试助手", "directories": {"output": "dist", "buildResources": "build"}, "files": [".next/**/*", "public/**/*", "electron/**/*", "package.json"], "asarUnpack": ["node_modules/next/**/*"], "extraResources": [{"from": "public", "to": "public", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-Setup.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeaderIcon": "build/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "JobPlus AI面试助手", "displayLanguageSelector": false, "installerLanguages": ["zh_CN"], "language": "2052"}}}