(()=>{var a={};a.id=429,a.ids=[429],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15234:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>J});var d=c(60687),e=c(43210),f=c.n(e),g=c(85814),h=c.n(g),i=c(63213),j=c(20769),k=c(29523),l=c(89667),m=c(44493),n=c(66458),o=c(25541),p=c(62688);let q=(0,p.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),r=(0,p.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var s=c(78200),t=c(40083);let u=(0,p.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var v=c(48730),w=c(86561),x=c(28947);let y=(0,p.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var z=c(31158);let A=(0,p.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var B=c(40228),C=c(57800),D=c(64398),E=c(13861),F=c(88233),G=c(41008),H=c(62694),I=c(94934);function J(){let{user:a}=(0,i.A)();(0,H.EM)();let[b,c]=(0,e.useState)([]),[g,p]=(0,e.useState)([]),[J,K]=(0,e.useState)(null),[L,M]=(0,e.useState)(!0),[N,O]=(0,e.useState)(""),[P,Q]=(0,e.useState)("all"),[R,S]=(0,e.useState)("all"),[T,U]=(0,e.useState)("date"),[V,W]=(0,e.useState)(!1),X=new n.E,Y=a=>{let b=Math.floor(a/3600),c=Math.floor(a%3600/60);return b>0?`${b}小时${c}分钟`:`${c}分钟`},Z=(a,b,c,e)=>(0,d.jsx)(m.Zp,{className:"group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-white to-blue-50 border-blue-200",children:(0,d.jsx)(m.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:a}),(0,d.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent",children:b})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[e&&(0,d.jsx)("div",{className:`p-2 rounded-full ${"up"===e?"text-green-600 bg-green-100":"down"===e?"text-red-600 bg-red-100":"text-gray-600 bg-gray-100"}`,children:"up"===e?(0,d.jsx)(o.A,{className:"h-4 w-4"}):"down"===e?(0,d.jsx)(q,{className:"h-4 w-4"}):(0,d.jsx)(r,{className:"h-4 w-4"})}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform",children:f().cloneElement(c,{className:"h-6 w-6 text-white"})})]})]})})});return L?(0,d.jsx)(j.A,{children:(0,d.jsx)(G.Z_,{children:(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"加载面试记录中..."})]})})})}):(0,d.jsx)(j.A,{children:(0,d.jsx)(G.Z_,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,d.jsx)(G._A,{direction:"down",children:(0,d.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(s.A,{className:"h-8 w-8 text-blue-600"}),(0,d.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,d.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,d.jsx)(h(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"控制台"}),(0,d.jsx)(h(),{href:"/resume",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"简历中心"}),(0,d.jsx)(h(),{href:"/interview",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试室"}),(0,d.jsx)(h(),{href:"/history",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"面试记录"}),(0,d.jsx)(k.$,{variant:"ghost",size:"icon",onClick:()=>I.f.signOut(),className:"hover:bg-red-50 hover:text-red-600",children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})]})]})})}),(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsx)(G._A,{delay:200,children:(0,d.jsxs)("div",{className:"mb-8 text-center",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:"面试记录"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"查看和管理您的面试历史记录，分析表现并持续改进"})]})}),J&&(0,d.jsx)(G.Oq,{delay:400,staggerDelay:100,children:(0,d.jsxs)("div",{className:"grid md:grid-cols-4 gap-6 mb-8",children:[Z("总面试次数",J.totalSessions,(0,d.jsx)(u,{className:"h-5 w-5"})),Z("总时长",Y(J.totalDuration),(0,d.jsx)(v.A,{className:"h-5 w-5"})),Z("平均评分",`${J.averageScore}/100`,(0,d.jsx)(w.A,{className:"h-5 w-5"})),Z("完成率",`${J.completionRate}%`,(0,d.jsx)(x.A,{className:"h-5 w-5"}))]})}),(0,d.jsx)(m.Zp,{className:"mb-6",children:(0,d.jsx)(m.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)(l.p,{placeholder:"搜索公司或职位...",value:N,onChange:a=>O(a.target.value),className:"pl-10"})]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)("select",{value:P,onChange:a=>Q(a.target.value),className:"px-3 py-2 border rounded-md",children:[(0,d.jsx)("option",{value:"all",children:"所有类型"}),(0,d.jsx)("option",{value:"technical",children:"技术面试"}),(0,d.jsx)("option",{value:"behavioral",children:"行为面试"}),(0,d.jsx)("option",{value:"mixed",children:"综合面试"})]}),(0,d.jsxs)("select",{value:R,onChange:a=>S(a.target.value),className:"px-3 py-2 border rounded-md",children:[(0,d.jsx)("option",{value:"all",children:"所有状态"}),(0,d.jsx)("option",{value:"completed",children:"已完成"}),(0,d.jsx)("option",{value:"active",children:"进行中"}),(0,d.jsx)("option",{value:"paused",children:"已暂停"}),(0,d.jsx)("option",{value:"cancelled",children:"已取消"})]}),(0,d.jsxs)("select",{value:T,onChange:a=>U(a.target.value),className:"px-3 py-2 border rounded-md",children:[(0,d.jsx)("option",{value:"date",children:"按日期排序"}),(0,d.jsx)("option",{value:"score",children:"按评分排序"}),(0,d.jsx)("option",{value:"duration",children:"按时长排序"})]}),(0,d.jsxs)(k.$,{onClick:()=>{let b=new Blob([X.exportSessions(a?.id)],{type:"application/json"}),c=URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=`jobplus-interviews-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(c)},variant:"outline",children:[(0,d.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"导出"]})]})]})})}),0===g.length?(0,d.jsx)(m.Zp,{children:(0,d.jsxs)(m.Wu,{className:"p-12 text-center",children:[(0,d.jsx)(A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无面试记录"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:0===b.length?"您还没有进行过面试练习":"没有找到符合条件的记录"}),(0,d.jsx)(h(),{href:"/interview",children:(0,d.jsx)(k.$,{children:"开始第一次面试"})})]})}):(0,d.jsx)("div",{className:"space-y-4",children:g.map(b=>(0,d.jsx)(m.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,d.jsx)(m.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[b.company," - ",b.position]}),(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${(a=>{switch(a){case"completed":return"text-green-600 bg-green-100";case"active":return"text-blue-600 bg-blue-100";case"paused":return"text-yellow-600 bg-yellow-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(b.status)}`,children:(a=>{switch(a){case"completed":return"已完成";case"active":return"进行中";case"paused":return"已暂停";case"cancelled":return"已取消";default:return"未知"}})(b.status)})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-600 mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(B.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:b.startTime.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(v.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:Y(b.duration)})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(C.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:(a=>{switch(a){case"technical":return"技术面试";case"behavioral":return"行为面试";case"mixed":return"综合面试";default:return a}})(b.interviewType)})]}),b.summary?.overallScore&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(D.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[b.summary.overallScore,"/100"]})]})]}),b.summary&&(0,d.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg mb-3",children:(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-gray-700",children:"优势："}),(0,d.jsx)("span",{className:"text-gray-600",children:b.summary.strongPoints.join("、")||"暂无"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-gray-700",children:"改进建议："}),(0,d.jsx)("span",{className:"text-gray-600",children:b.summary.improvementAreas.join("、")||"暂无"})]})]})}),b.userNotes&&(0,d.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,d.jsx)("span",{className:"font-medium text-blue-900",children:"备注："}),(0,d.jsx)("span",{className:"text-blue-800",children:b.userNotes})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,d.jsx)(h(),{href:`/history/${b.id}`,children:(0,d.jsxs)(k.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"查看详情"]})}),(0,d.jsx)(k.$,{variant:"outline",size:"sm",onClick:()=>(b=>{if(confirm("确定要删除这个面试记录吗？此操作无法撤销。"))if(X.deleteSession(b)){M(!0);try{let b=X.getAllSessions(a?.id),d=X.getStats(a?.id);c(b),K(d)}catch(a){console.error("Failed to load interview data:",a)}finally{M(!1)}}else alert("删除失败，请重试。")})(b.id),className:"text-red-600 hover:text-red-700",children:(0,d.jsx)(F.A,{className:"h-4 w-4"})})]})]})})},b.id))})]})]})})})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26515:(a,b,c)=>{Promise.resolve().then(c.bind(c,32487))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28947:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32487:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\jobplus\\\\1-前端服务\\\\ai-jobplus-web6\\\\jobplus-web\\\\src\\\\app\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\page.tsx","default")},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},57800:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},57898:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,32487)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/history/page",pathname:"/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/history/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63467:(a,b,c)=>{Promise.resolve().then(c.bind(c,15234))},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66458:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});class d{saveSession(a){try{let b=this.getAllSessions(),c=b.findIndex(b=>b.id===a.id);return c>=0?b[c]=a:b.push(a),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(b)),this.updateStats(),!0}catch(a){return console.error("Failed to save interview session:",a),!1}}getAllSessions(a){try{let b=localStorage.getItem(this.STORAGE_KEY);if(!b)return[];let c=JSON.parse(b).map(a=>({...a,startTime:new Date(a.startTime),endTime:a.endTime?new Date(a.endTime):void 0,messages:a.messages.map(a=>({...a,timestamp:new Date(a.timestamp)}))}));if(a)return c.filter(b=>b.userId===a);return c}catch(a){return console.error("Failed to load interview sessions:",a),[]}}getSessionById(a){return this.getAllSessions().find(b=>b.id===a)||null}deleteSession(a){try{let b=this.getAllSessions().filter(b=>b.id!==a);return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(b)),this.updateStats(),!0}catch(a){return console.error("Failed to delete interview session:",a),!1}}updateSessionNotes(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].userNotes=b,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),!0;return!1}catch(a){return console.error("Failed to update session notes:",a),!1}}updateSessionRating(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].rating=Math.max(1,Math.min(5,b)),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),this.updateStats(),!0;return!1}catch(a){return console.error("Failed to update session rating:",a),!1}}updateSessionTags(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].tags=b,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),!0;return!1}catch(a){return console.error("Failed to update session tags:",a),!1}}getFilteredSessions(a){let b=this.getAllSessions(a.userId);return a.company&&(b=b.filter(b=>b.company.toLowerCase().includes(a.company.toLowerCase()))),a.position&&(b=b.filter(b=>b.position.toLowerCase().includes(a.position.toLowerCase()))),a.interviewType&&(b=b.filter(b=>b.interviewType===a.interviewType)),a.status&&(b=b.filter(b=>b.status===a.status)),a.dateFrom&&(b=b.filter(b=>b.startTime>=a.dateFrom)),a.dateTo&&(b=b.filter(b=>b.startTime<=a.dateTo)),a.minRating&&(b=b.filter(b=>(b.rating||0)>=a.minRating)),a.tags&&a.tags.length>0&&(b=b.filter(b=>b.tags&&b.tags.some(b=>a.tags.includes(b)))),b.sort((a,b)=>b.startTime.getTime()-a.startTime.getTime())}getStats(a){try{let b=this.getAllSessions(a),c=b.filter(a=>"completed"===a.status);if(0===c.length)return{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]};let d=c.reduce((a,b)=>a+b.duration,0),e=c.filter(a=>a.summary?.overallScore).reduce((a,b)=>a+(b.summary?.overallScore||0),0)/c.filter(a=>a.summary?.overallScore).length||0,f=new Map;c.forEach(a=>{f.set(a.company,(f.get(a.company)||0)+1)});let g=Array.from(f.entries()).map(([a,b])=>({company:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,5),h=new Map;c.forEach(a=>{h.set(a.position,(h.get(a.position)||0)+1)});let i=Array.from(h.entries()).map(([a,b])=>({position:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,5),j=this.calculateMonthlyProgress(c),k=this.calculateSkillProgress(c);return{totalSessions:b.length,totalDuration:d,averageScore:Math.round(e),completionRate:Math.round(c.length/b.length*100),topCompanies:g,topPositions:i,monthlyProgress:j,skillProgress:k}}catch(a){return console.error("Failed to calculate stats:",a),{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]}}}calculateMonthlyProgress(a){let b=new Map;return a.forEach(a=>{let c=a.startTime.toISOString().substring(0,7),d=b.get(c)||{sessions:0,totalScore:0,count:0};d.sessions++,a.summary?.overallScore&&(d.totalScore+=a.summary.overallScore,d.count++),b.set(c,d)}),Array.from(b.entries()).map(([a,b])=>({month:a,sessions:b.sessions,avgScore:b.count>0?Math.round(b.totalScore/b.count):0})).sort((a,b)=>a.month.localeCompare(b.month)).slice(-6)}calculateSkillProgress(a){return["技术能力","沟通表达","逻辑思维","团队协作","学习能力"].map(b=>{let c=a.slice(-5).filter(a=>a.summary?.overallScore).map(a=>a.summary.overallScore),d=c.length>0?Math.round(c.reduce((a,b)=>a+b,0)/c.length):0,e="stable";if(c.length>=3){let a=c.slice(0,Math.floor(c.length/2)),b=c.slice(Math.floor(c.length/2)),d=a.reduce((a,b)=>a+b,0)/a.length,f=b.reduce((a,b)=>a+b,0)/b.length;f>d+5?e="up":f<d-5&&(e="down")}return{skill:b,score:d,trend:e}})}updateStats(){}exportSessions(a){return JSON.stringify(this.getAllSessions(a),null,2)}importSessions(a){try{let b=JSON.parse(a),c=this.getAllSessions(),d=new Map;c.forEach(a=>{d.set(a.id,a)}),b.forEach(a=>{d.set(a.id,a)});let e=Array.from(d.values());return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e)),this.updateStats(),!0}catch(a){return console.error("Failed to import sessions:",a),!1}}clearAllData(){try{return localStorage.removeItem(this.STORAGE_KEY),localStorage.removeItem(this.STATS_KEY),!0}catch(a){return console.error("Failed to clear data:",a),!1}}constructor(){this.STORAGE_KEY="jobplus_interview_sessions",this.STATS_KEY="jobplus_interview_stats"}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,852,814,300,219],()=>b(b.s=57898));module.exports=c})();