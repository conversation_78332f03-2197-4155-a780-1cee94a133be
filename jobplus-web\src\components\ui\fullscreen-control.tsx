'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Maximize, Minimize } from 'lucide-react';
import { useElectronAPI } from '@/hooks/useElectron';

export function FullscreenControl() {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const electronAPI = useElectronAPI();

  useEffect(() => {
    // Check initial fullscreen state
    if (electronAPI) {
      electronAPI.isFullscreen().then(setIsFullscreen);
    }
  }, [electronAPI]);

  const toggleFullscreen = async () => {
    if (electronAPI) {
      try {
        const newFullscreenState = await electronAPI.toggleFullscreen();
        setIsFullscreen(newFullscreenState);
      } catch (error) {
        console.error('Failed to toggle fullscreen:', error);
      }
    }
  };

  // Only show the control if we're in Electron
  if (!electronAPI) {
    return null;
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleFullscreen}
      className="hover:bg-gray-100 dark:hover:bg-gray-800"
      title={isFullscreen ? '退出全屏' : '进入全屏'}
    >
      {isFullscreen ? (
        <Minimize className="h-4 w-4" />
      ) : (
        <Maximize className="h-4 w-4" />
      )}
    </Button>
  );
}

export function FullscreenToggle() {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const electronAPI = useElectronAPI();

  useEffect(() => {
    if (electronAPI) {
      electronAPI.isFullscreen().then(setIsFullscreen);
    }
  }, [electronAPI]);

  const toggleFullscreen = async () => {
    if (electronAPI) {
      try {
        const newFullscreenState = await electronAPI.toggleFullscreen();
        setIsFullscreen(newFullscreenState);
      } catch (error) {
        console.error('Failed to toggle fullscreen:', error);
      }
    }
  };

  // Only show if we're in Electron
  if (!electronAPI) {
    return null;
  }

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {isFullscreen ? '全屏模式' : '窗口模式'}
      </span>
      <Button
        variant="outline"
        size="sm"
        onClick={toggleFullscreen}
        className="flex items-center space-x-2"
      >
        {isFullscreen ? (
          <>
            <Minimize className="h-4 w-4" />
            <span>退出全屏</span>
          </>
        ) : (
          <>
            <Maximize className="h-4 w-4" />
            <span>全屏</span>
          </>
        )}
      </Button>
    </div>
  );
}
