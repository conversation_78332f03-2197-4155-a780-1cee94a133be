(()=>{var a={};a.id=369,a.ids=[369],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["history",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,93869)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/history/[id]/page",pathname:"/history/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/history/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13403:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>J});var d=c(60687),e=c(43210),f=c(16189),g=c(85814),h=c.n(g),i=c(63213),j=c(20769),k=c(29523),l=c(44493),m=c(66458),n=c(58869),o=c(83753),p=c(58887),q=c(62688);let r=(0,q.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var s=c(64398),t=c(28559),u=c(78200),v=c(31158),w=c(86561),x=c(5336),y=c(28947),z=c(15807),A=c(63143),B=c(8819),C=c(11860);let D=(0,q.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var E=c(57800),F=c(40228),G=c(48730),H=c(41008),I=c(62694);function J(){let a;(0,f.useParams)(),(0,f.useRouter)();let{user:b}=(0,i.A)();(0,I.EM)();let[c,g]=(0,e.useState)(null),[q,J]=(0,e.useState)(!0),[K,L]=(0,e.useState)(!1),[M,N]=(0,e.useState)(""),[O,P]=(0,e.useState)(0),Q=new m.E,R=a=>{let b=Math.floor(a/3600),c=Math.floor(a%3600/60);return b>0?`${b}小时${c}分钟`:`${c}分钟`},S=a=>a.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return q?(0,d.jsx)(j.A,{children:(0,d.jsx)(H.Z_,{children:(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"加载面试详情中..."})]})})})}):c?(0,d.jsx)(j.A,{children:(0,d.jsx)(H.Z_,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,d.jsx)("header",{className:"bg-white border-b",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(h(),{href:"/history",children:(0,d.jsx)(k.$,{variant:"ghost",size:"icon",children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"JobPlus"})]})]}),(0,d.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,d.jsx)(h(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900",children:"控制台"}),(0,d.jsx)(h(),{href:"/history",className:"text-gray-600 hover:text-gray-900",children:"面试记录"}),(0,d.jsx)("span",{className:"text-blue-600 font-medium",children:"详情"})]})]})}),(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:[c.company," - ",c.position]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-gray-600",children:[(0,d.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${(a=>{switch(a){case"completed":return"text-green-600 bg-green-100";case"active":return"text-blue-600 bg-blue-100";case"paused":return"text-yellow-600 bg-yellow-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(c.status)}`,children:(a=>{switch(a){case"completed":return"已完成";case"active":return"进行中";case"paused":return"已暂停";case"cancelled":return"已取消";default:return"未知"}})(c.status)}),(0,d.jsx)("span",{children:(a=>{switch(a){case"technical":return"技术面试";case"behavioral":return"行为面试";case"mixed":return"综合面试";default:return a}})(c.interviewType)}),(0,d.jsx)("span",{children:S(c.startTime)}),(0,d.jsx)("span",{children:R(c.duration)})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)(k.$,{onClick:()=>{if(c){let a=new Blob([JSON.stringify(c,null,2)],{type:"application/json"}),b=URL.createObjectURL(a),d=document.createElement("a");d.href=b,d.download=`interview-${c.company}-${c.position}-${c.startTime.toISOString().split("T")[0]}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(b)}},variant:"outline",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"导出"]})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"评分："}),(a=a=>{c&&Q.updateSessionRating(c.id,a)&&(g({...c,rating:a}),P(a))},(0,d.jsx)("div",{className:"flex items-center space-x-1",children:[1,2,3,4,5].map(b=>(0,d.jsx)("button",{onClick:()=>a(b),className:`p-1 rounded ${b<=O?"text-yellow-500":"text-gray-300"} hover:text-yellow-400 transition-colors`,children:(0,d.jsx)(s.A,{className:"h-5 w-5 fill-current"})},b))})),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:O>0?`${O}/5 星`:"未评分"})]})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[c.summary&&(0,d.jsxs)(l.Zp,{children:[(0,d.jsx)(l.aR,{children:(0,d.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(w.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"面试总结"})]})}),(0,d.jsxs)(l.Wu,{children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:c.summary.overallScore}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"综合评分"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-1",children:c.summary.totalResponses}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"回答次数"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[c.summary.strongPoints.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"font-medium text-green-900 mb-2 flex items-center",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"表现优势"]}),(0,d.jsx)("ul",{className:"text-sm text-green-800 space-y-1",children:c.summary.strongPoints.map((a,b)=>(0,d.jsxs)("li",{children:["• ",a]},b))})]}),c.summary.improvementAreas.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"font-medium text-orange-900 mb-2 flex items-center",children:[(0,d.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"改进建议"]}),(0,d.jsx)("ul",{className:"text-sm text-orange-800 space-y-1",children:c.summary.improvementAreas.map((a,b)=>(0,d.jsxs)("li",{children:["• ",a]},b))})]}),c.summary.keyInsights&&c.summary.keyInsights.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"font-medium text-blue-900 mb-2 flex items-center",children:[(0,d.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"关键洞察"]}),(0,d.jsx)("ul",{className:"text-sm text-blue-800 space-y-1",children:c.summary.keyInsights.map((a,b)=>(0,d.jsxs)("li",{children:["• ",a]},b))})]})]})]})]}),(0,d.jsxs)(l.Zp,{children:[(0,d.jsxs)(l.aR,{children:[(0,d.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(p.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"对话记录"})]}),(0,d.jsx)(l.BT,{children:"完整的面试对话历史，包括您的回答和AI建议"})]}),(0,d.jsx)(l.Wu,{children:(0,d.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===c.messages.length?(0,d.jsx)("p",{className:"text-gray-500 text-center py-8",children:"暂无对话记录"}):c.messages.map((a,b)=>(0,d.jsx)("div",{className:`flex ${"user"===a.type?"justify-end":"justify-start"} mb-4`,children:(0,d.jsxs)("div",{className:`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${"user"===a.type?"bg-blue-600 text-white":"ai"===a.type?"bg-green-100 text-green-900":"bg-gray-100 text-gray-900"}`,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:["user"===a.type?(0,d.jsx)(n.A,{className:"h-4 w-4"}):"ai"===a.type?(0,d.jsx)(o.A,{className:"h-4 w-4"}):(0,d.jsx)(p.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-xs opacity-75",children:a.timestamp.toLocaleTimeString()}),(0,d.jsx)(k.$,{variant:"ghost",size:"sm",onClick:()=>{var b;return b=a.content,void navigator.clipboard.writeText(b).then(()=>{alert("已复制到剪贴板")})},className:"h-6 w-6 p-0 opacity-50 hover:opacity-100",children:(0,d.jsx)(r,{className:"h-3 w-3"})})]}),(0,d.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:a.content}),a.suggestions&&a.suggestions.length>0&&(0,d.jsx)("div",{className:"mt-2 space-y-1",children:a.suggestions.map((a,b)=>(0,d.jsxs)("div",{className:"text-xs bg-white bg-opacity-20 px-2 py-1 rounded",children:["\uD83D\uDCA1 ",a]},b))})]})},a.id))})})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(l.Zp,{children:[(0,d.jsx)(l.aR,{children:(0,d.jsxs)(l.ZB,{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(A.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"个人备注"})]}),!K&&(0,d.jsx)(k.$,{variant:"ghost",size:"sm",onClick:()=>L(!0),children:(0,d.jsx)(A.A,{className:"h-4 w-4"})})]})}),(0,d.jsx)(l.Wu,{children:K?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("textarea",{value:M,onChange:a=>N(a.target.value),placeholder:"添加您的备注...",className:"w-full p-3 border rounded-md resize-none",rows:4}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(k.$,{onClick:()=>{c&&(Q.updateSessionNotes(c.id,M)?(g({...c,userNotes:M}),L(!1)):alert("保存失败，请重试"))},size:"sm",children:[(0,d.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"保存"]}),(0,d.jsxs)(k.$,{variant:"outline",size:"sm",onClick:()=>{L(!1),N(c.userNotes||"")},children:[(0,d.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"取消"]})]})]}):(0,d.jsx)("div",{children:M?(0,d.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:M}):(0,d.jsx)("p",{className:"text-sm text-gray-500 italic",children:"暂无备注"})})})]}),(0,d.jsxs)(l.Zp,{children:[(0,d.jsx)(l.aR,{children:(0,d.jsx)(l.ZB,{children:"面试信息"})}),(0,d.jsxs)(l.Wu,{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(D,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"font-medium",children:"公司："}),(0,d.jsx)("span",{children:c.company})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(E.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"font-medium",children:"职位："}),(0,d.jsx)("span",{children:c.position})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(F.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"font-medium",children:"开始时间："}),(0,d.jsx)("span",{children:S(c.startTime)})]}),c.endTime&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(F.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"font-medium",children:"结束时间："}),(0,d.jsx)("span",{children:S(c.endTime)})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(G.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"font-medium",children:"时长："}),(0,d.jsx)("span",{children:R(c.duration)})]})]})]})]})]})]})]})})}):(0,d.jsx)(j.A,{children:(0,d.jsx)(H.Z_,{children:(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"面试记录不存在"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"请检查链接是否正确"}),(0,d.jsx)(h(),{href:"/history",children:(0,d.jsx)(k.$,{children:"返回面试记录"})})]})})})})}},15807:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687);c(43210);var e=c(16189),f=c(63213);function g({children:a,redirectTo:b="/auth/login"}){let{user:c,loading:g}=(0,f.A)();return((0,e.useRouter)(),g)?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):c?(0,d.jsx)(d.Fragment,{children:a}):null}},24224:(a,b,c)=>{"use strict";c.d(b,{F:()=>g});var d=c(49384);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.$,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(81391),g=c(24224),h=c(4780);let i=(0,g.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33834:(a,b,c)=>{Promise.resolve().then(c.bind(c,93869))},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41008:(a,b,c)=>{"use strict";c.d(b,{Oq:()=>j,Z_:()=>h,_A:()=>i});var d=c(60687),e=c(43210),f=c.n(e),g=c(4780);function h({children:a,className:b,delay:c=0}){let[f,h]=(0,e.useState)(!1);return(0,d.jsx)("div",{className:(0,g.cn)("transition-all duration-500 ease-out",f?"opacity-100 translate-y-0":"opacity-0 translate-y-4",b),children:a})}function i({children:a,direction:b="up",delay:c=0,duration:f=500,className:h}){let[i,j]=(0,e.useState)(!1);return(0,d.jsx)("div",{className:(0,g.cn)("transition-all ease-out",i?"opacity-100 translate-x-0 translate-y-0":`opacity-0 ${({up:"translate-y-4",down:"-translate-y-4",left:"translate-x-4",right:"-translate-x-4"})[b]}`,h),style:{transitionDuration:`${f}ms`},children:a})}function j({children:a,delay:b=0,staggerDelay:c=100,className:e}){let g=f().Children.toArray(a);return(0,d.jsx)("div",{className:e,children:g.map((a,e)=>(0,d.jsx)(i,{delay:b+e*c,children:a},e))})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},57800:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58869:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-square",[["path",{d:"M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z",key:"18887p"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66458:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});class d{saveSession(a){try{let b=this.getAllSessions(),c=b.findIndex(b=>b.id===a.id);return c>=0?b[c]=a:b.push(a),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(b)),this.updateStats(),!0}catch(a){return console.error("Failed to save interview session:",a),!1}}getAllSessions(a){try{let b=localStorage.getItem(this.STORAGE_KEY);if(!b)return[];let c=JSON.parse(b).map(a=>({...a,startTime:new Date(a.startTime),endTime:a.endTime?new Date(a.endTime):void 0,messages:a.messages.map(a=>({...a,timestamp:new Date(a.timestamp)}))}));if(a)return c.filter(b=>b.userId===a);return c}catch(a){return console.error("Failed to load interview sessions:",a),[]}}getSessionById(a){return this.getAllSessions().find(b=>b.id===a)||null}deleteSession(a){try{let b=this.getAllSessions().filter(b=>b.id!==a);return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(b)),this.updateStats(),!0}catch(a){return console.error("Failed to delete interview session:",a),!1}}updateSessionNotes(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].userNotes=b,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),!0;return!1}catch(a){return console.error("Failed to update session notes:",a),!1}}updateSessionRating(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].rating=Math.max(1,Math.min(5,b)),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),this.updateStats(),!0;return!1}catch(a){return console.error("Failed to update session rating:",a),!1}}updateSessionTags(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].tags=b,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),!0;return!1}catch(a){return console.error("Failed to update session tags:",a),!1}}getFilteredSessions(a){let b=this.getAllSessions(a.userId);return a.company&&(b=b.filter(b=>b.company.toLowerCase().includes(a.company.toLowerCase()))),a.position&&(b=b.filter(b=>b.position.toLowerCase().includes(a.position.toLowerCase()))),a.interviewType&&(b=b.filter(b=>b.interviewType===a.interviewType)),a.status&&(b=b.filter(b=>b.status===a.status)),a.dateFrom&&(b=b.filter(b=>b.startTime>=a.dateFrom)),a.dateTo&&(b=b.filter(b=>b.startTime<=a.dateTo)),a.minRating&&(b=b.filter(b=>(b.rating||0)>=a.minRating)),a.tags&&a.tags.length>0&&(b=b.filter(b=>b.tags&&b.tags.some(b=>a.tags.includes(b)))),b.sort((a,b)=>b.startTime.getTime()-a.startTime.getTime())}getStats(a){try{let b=this.getAllSessions(a),c=b.filter(a=>"completed"===a.status);if(0===c.length)return{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]};let d=c.reduce((a,b)=>a+b.duration,0),e=c.filter(a=>a.summary?.overallScore).reduce((a,b)=>a+(b.summary?.overallScore||0),0)/c.filter(a=>a.summary?.overallScore).length||0,f=new Map;c.forEach(a=>{f.set(a.company,(f.get(a.company)||0)+1)});let g=Array.from(f.entries()).map(([a,b])=>({company:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,5),h=new Map;c.forEach(a=>{h.set(a.position,(h.get(a.position)||0)+1)});let i=Array.from(h.entries()).map(([a,b])=>({position:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,5),j=this.calculateMonthlyProgress(c),k=this.calculateSkillProgress(c);return{totalSessions:b.length,totalDuration:d,averageScore:Math.round(e),completionRate:Math.round(c.length/b.length*100),topCompanies:g,topPositions:i,monthlyProgress:j,skillProgress:k}}catch(a){return console.error("Failed to calculate stats:",a),{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]}}}calculateMonthlyProgress(a){let b=new Map;return a.forEach(a=>{let c=a.startTime.toISOString().substring(0,7),d=b.get(c)||{sessions:0,totalScore:0,count:0};d.sessions++,a.summary?.overallScore&&(d.totalScore+=a.summary.overallScore,d.count++),b.set(c,d)}),Array.from(b.entries()).map(([a,b])=>({month:a,sessions:b.sessions,avgScore:b.count>0?Math.round(b.totalScore/b.count):0})).sort((a,b)=>a.month.localeCompare(b.month)).slice(-6)}calculateSkillProgress(a){return["技术能力","沟通表达","逻辑思维","团队协作","学习能力"].map(b=>{let c=a.slice(-5).filter(a=>a.summary?.overallScore).map(a=>a.summary.overallScore),d=c.length>0?Math.round(c.reduce((a,b)=>a+b,0)/c.length):0,e="stable";if(c.length>=3){let a=c.slice(0,Math.floor(c.length/2)),b=c.slice(Math.floor(c.length/2)),d=a.reduce((a,b)=>a+b,0)/a.length,f=b.reduce((a,b)=>a+b,0)/b.length;f>d+5?e="up":f<d-5&&(e="down")}return{skill:b,score:d,trend:e}})}updateStats(){}exportSessions(a){return JSON.stringify(this.getAllSessions(a),null,2)}importSessions(a){try{let b=JSON.parse(a),c=this.getAllSessions(),d=new Map;c.forEach(a=>{d.set(a.id,a)}),b.forEach(a=>{d.set(a.id,a)});let e=Array.from(d.values());return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e)),this.updateStats(),!0}catch(a){return console.error("Failed to import sessions:",a),!1}}clearAllData(){try{return localStorage.removeItem(this.STORAGE_KEY),localStorage.removeItem(this.STATS_KEY),!0}catch(a){return console.error("Failed to clear data:",a),!1}}constructor(){this.STORAGE_KEY="jobplus_interview_sessions",this.STATS_KEY="jobplus_interview_stats"}}},74501:(a,b,c)=>{Promise.resolve().then(c.bind(c,13403))},78200:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]])},81391:(a,b,c)=>{"use strict";c.d(b,{DX:()=>h,TL:()=>g});var d=c(43210);function e(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var f=c(60687);function g(a){let b=function(a){let b=d.forwardRef((a,b)=>{let{children:c,...f}=a;if(d.isValidElement(c)){var g;let a,h,i=(g=c,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,c.props);return c.type!==d.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=e(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():e(a[b],null)}}}}(b,i):i),d.cloneElement(c,j)}return d.Children.count(c)>1?d.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=d.forwardRef((a,c)=>{let{children:e,...g}=a,h=d.Children.toArray(e),i=h.find(j);if(i){let a=i.props.children,e=h.map(b=>b!==i?b:d.Children.count(a)>1?d.Children.only(null):d.isValidElement(a)?a.props.children:null);return(0,f.jsx)(b,{...g,ref:c,children:d.isValidElement(a)?d.cloneElement(a,void 0,e):null})}return(0,f.jsx)(b,{...g,ref:c,children:e})});return c.displayName=`${a}.Slot`,c}var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return d.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}},83753:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},93869:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\jobplus\\\\1-前端服务\\\\ai-jobplus-web6\\\\jobplus-web\\\\src\\\\app\\\\history\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\history\\[id]\\page.tsx","default")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,852,814,300],()=>b(b.s=2014));module.exports=c})();