const { spawn } = require('child_process');

console.log('🚀 Starting Electron...');

// Set environment variable for development
process.env.NODE_ENV = 'development';

// Start Electron
const electronProcess = spawn('npx', ['electron', '.'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'development'
  }
});

electronProcess.on('close', (code) => {
  console.log(`Electron process exited with code ${code}`);
});

electronProcess.on('error', (error) => {
  console.error('Failed to start Electron:', error);
});
