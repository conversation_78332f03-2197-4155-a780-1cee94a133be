const { exec } = require('child_process');

console.log('🔍 Verifying Electron integration...\n');

// Check if development server is running
const checkDevServer = () => {
  return new Promise((resolve) => {
    exec('netstat -an | findstr :3000', (error, stdout) => {
      if (stdout.includes('LISTENING')) {
        console.log('✅ Development server is running on port 3000');
        resolve(true);
      } else {
        console.log('❌ Development server is not running');
        resolve(false);
      }
    });
  });
};

// Check if Electron processes are running
const checkElectronProcesses = () => {
  return new Promise((resolve) => {
    exec('tasklist | findstr electron', (error, stdout) => {
      if (stdout && stdout.includes('electron.exe')) {
        const processes = stdout.split('\n').filter(line => line.includes('electron.exe'));
        console.log(`✅ Found ${processes.length} Electron processes running`);
        processes.forEach((process, index) => {
          const parts = process.trim().split(/\s+/);
          if (parts.length >= 2) {
            console.log(`   Process ${index + 1}: PID ${parts[1]}`);
          }
        });
        resolve(true);
      } else {
        console.log('❌ No Electron processes found');
        resolve(false);
      }
    });
  });
};

// Main verification function
const main = async () => {
  console.log('📋 Checking system status...\n');
  
  const devServerRunning = await checkDevServer();
  const electronRunning = await checkElectronProcesses();
  
  console.log('\n📊 Summary:');
  console.log(`   Development Server: ${devServerRunning ? '✅ Running' : '❌ Not Running'}`);
  console.log(`   Electron Application: ${electronRunning ? '✅ Running' : '❌ Not Running'}`);
  
  if (devServerRunning && electronRunning) {
    console.log('\n🎉 Electron integration is working correctly!');
    console.log('   The JobPlus desktop application should be visible on your screen.');
  } else {
    console.log('\n⚠️  Some components are not running:');
    if (!devServerRunning) {
      console.log('   - Start development server: npm run dev');
    }
    if (!electronRunning) {
      console.log('   - Start Electron: npx electron .');
    }
  }
};

main().catch(console.error);
