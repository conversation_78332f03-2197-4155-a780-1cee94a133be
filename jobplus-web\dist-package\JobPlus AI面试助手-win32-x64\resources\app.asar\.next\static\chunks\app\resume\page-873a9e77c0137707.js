(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[736],{283:(e,s,r)=>{"use strict";r.d(s,{A:()=>n,AuthProvider:()=>d});var a=r(5155),t=r(2115),l=r(7292);let i=(0,t.createContext)({user:null,loading:!0,signOut:async()=>{}}),n=()=>{let e=(0,t.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},d=e=>{let{children:s}=e,[r,n]=(0,t.useState)(null),[d,c]=(0,t.useState)(!0);(0,t.useEffect)(()=>{(async()=>{var e;let{data:{session:s}}=await l.j.auth.getSession();n(null!=(e=null==s?void 0:s.user)?e:null),c(!1)})();let{data:{subscription:e}}=l.j.auth.onAuthStateChange(async(e,s)=>{var r;n(null!=(r=null==s?void 0:s.user)?r:null),c(!1)});return()=>e.unsubscribe()},[]);let o=async()=>{await l.j.auth.signOut()};return(0,a.jsx)(i.Provider,{value:{user:r,loading:d,signOut:o},children:s})}},285:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var a=r(5155),t=r(2115),l=r(4624),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:r,variant:t,size:i,asChild:c=!1,...o}=e,u=c?l.DX:"button";return(0,a.jsx)(u,{className:(0,n.cn)(d({variant:t,size:i,className:r})),ref:s,...o})});c.displayName="Button"},688:(e,s,r)=>{"use strict";r.d(s,{Oq:()=>d,Z_:()=>i,_A:()=>n});var a=r(5155),t=r(2115),l=r(9434);function i(e){let{children:s,className:r,delay:i=0}=e,[n,d]=(0,t.useState)(!1);return(0,t.useEffect)(()=>{let e=setTimeout(()=>{d(!0)},i);return()=>clearTimeout(e)},[i]),(0,a.jsx)("div",{className:(0,l.cn)("transition-all duration-500 ease-out",n?"opacity-100 translate-y-0":"opacity-0 translate-y-4",r),children:s})}function n(e){let{children:s,direction:r="up",delay:i=0,duration:n=500,className:d}=e,[c,o]=(0,t.useState)(!1);return(0,t.useEffect)(()=>{let e=setTimeout(()=>{o(!0)},i);return()=>clearTimeout(e)},[i]),(0,a.jsx)("div",{className:(0,l.cn)("transition-all ease-out",c?"opacity-100 translate-x-0 translate-y-0":"opacity-0 ".concat({up:"translate-y-4",down:"-translate-y-4",left:"translate-x-4",right:"-translate-x-4"}[r]),d),style:{transitionDuration:"".concat(n,"ms")},children:s})}function d(e){let{children:s,delay:r=0,staggerDelay:l=100,className:i}=e,d=t.Children.toArray(s);return(0,a.jsx)("div",{className:i,children:d.map((e,s)=>(0,a.jsx)(n,{delay:r+s*l,children:e},s))})}},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var a=r(5155),t=r(2115),l=r(9434);let i=t.forwardRef((e,s)=>{let{className:r,type:t,...i}=e;return(0,a.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...i})});i.displayName="Input"},5057:(e,s,r)=>{"use strict";r.d(s,{J:()=>c});var a=r(5155),t=r(2115),l=r(7073),i=r(2085),n=r(9434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,n.cn)(d(),r),...t})});c.displayName=l.b.displayName},5458:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>R});var a=r(5155),t=r(2115),l=r(6874),i=r.n(l),n=r(283),d=r(9053),c=r(285),o=r(2523),u=r(5057),x=r(6695),m=r(9376),h=r(3717),p=r(4835),g=r(7434),f=r(1154),j=r(9869),b=r(4229),v=r(1007),y=r(7949),N=r(4616),w=r(2525),C=r(7576),k=r(9621),A=r(688),_=r(6982),S=r(7292);function R(){let{user:e}=(0,n.A)();(0,_.EM)();let s=(0,t.useRef)(null),[r,l]=(0,t.useState)(!1),[R,J]=(0,t.useState)(!1),[P,T]=(0,t.useState)(!1),[D,E]=(0,t.useState)(!1),[U,Z]=(0,t.useState)({name:"",phone:"",email:(null==e?void 0:e.email)||"",city:""}),[F,B]=(0,t.useState)([]),[O,$]=(0,t.useState)([]),[z,W]=(0,t.useState)([]),[I,L]=(0,t.useState)({programming_languages:[],frameworks:[],databases:[],tools:[]}),M=async s=>{var r;if(null==(r=s.target.files)?void 0:r[0]){l(!0),J(!0);try{await new Promise(e=>setTimeout(e,3e3)),Z({name:"张三",phone:"13800138000",email:(null==e?void 0:e.email)||"",city:"北京"}),B([{id:"1",school:"清华大学",degree:"本科",major:"计算机科学与技术",start_date:"2018-09",end_date:"2022-06"}]),$([{id:"1",company:"阿里巴巴",position:"前端工程师",start_date:"2022-07",end_date:"2024-08",responsibilities:["负责电商平台前端开发","使用React和TypeScript构建用户界面","优化页面性能，提升用户体验"]}]),W([{id:"1",name:"电商管理系统",role:"前端负责人",description:"基于React的电商后台管理系统，支持商品管理、订单处理等功能",technologies:["React","TypeScript","Ant Design","Redux"]}]),L({programming_languages:["JavaScript","TypeScript","Python"],frameworks:["React","Vue.js","Node.js"],databases:["MySQL","MongoDB","Redis"],tools:["Git","Docker","Webpack"]}),E(!0),T(!0)}catch(e){console.error("Upload failed:",e)}finally{l(!1),J(!1)}}},G=(e,s,r)=>{B(F.map(a=>a.id===e?{...a,[s]:r}:a))},Q=(e,s,r)=>{$(O.map(a=>a.id===e?{...a,[s]:r}:a))},V=async()=>{try{console.log("Saving resume...",{personalInfo:U,education:F,workExperience:O,projects:z,skills:I}),T(!1)}catch(e){console.error("Save failed:",e)}};return(0,a.jsx)(d.A,{children:(0,a.jsx)(A.Z_,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,a.jsx)(A._A,{direction:"down",children:(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,a.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"控制台"}),(0,a.jsx)(i(),{href:"/resume",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"简历中心"}),(0,a.jsx)(i(),{href:"/interview",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试室"}),(0,a.jsx)(i(),{href:"/history",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试记录"}),D&&(0,a.jsxs)(c.$,{onClick:()=>T(!P),variant:P?"default":"outline",className:"ml-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),P?"预览模式":"编辑模式"]}),(0,a.jsx)(c.$,{variant:"ghost",size:"icon",onClick:()=>S.f.signOut(),className:"hover:bg-red-50 hover:text-red-600",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)(A._A,{delay:200,children:(0,a.jsxs)("div",{className:"mb-8 text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:"简历管理中心"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"上传并管理您的简历，AI将帮助您优化内容并提取关键信息"})]})}),D?(0,a.jsxs)("div",{className:"space-y-6",children:[P&&(0,a.jsx)(A._A,{delay:400,children:(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(c.$,{onClick:V,className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"保存更改"]})})}),(0,a.jsx)(A._A,{delay:500,children:(0,a.jsxs)(x.Zp,{className:"shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,a.jsx)(x.aR,{className:"bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:(0,a.jsxs)(x.ZB,{className:"flex items-center space-x-2 text-xl text-gray-800",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,a.jsx)(v.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{children:"个人信息"})]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"name",children:"姓名"}),(0,a.jsx)(o.p,{id:"name",value:U.name,onChange:e=>Z(s=>({...s,name:e.target.value})),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"phone",children:"手机号码"}),(0,a.jsx)(o.p,{id:"phone",value:U.phone,onChange:e=>Z(s=>({...s,phone:e.target.value})),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"邮箱地址"}),(0,a.jsx)(o.p,{id:"email",value:U.email,onChange:e=>Z(s=>({...s,email:e.target.value})),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"city",children:"所在城市"}),(0,a.jsx)(o.p,{id:"city",value:U.city,onChange:e=>Z(s=>({...s,city:e.target.value})),disabled:!P})]})]})})]})}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(x.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"教育经历"})]}),P&&(0,a.jsxs)(c.$,{onClick:()=>{B([...F,{id:Date.now().toString(),school:"",degree:"",major:"",start_date:"",end_date:""}])},size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"添加"]})]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:F.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"学校"}),(0,a.jsx)(o.p,{value:e.school,onChange:s=>G(e.id,"school",s.target.value),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"学历"}),(0,a.jsx)(o.p,{value:e.degree,onChange:s=>G(e.id,"degree",s.target.value),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"专业"}),(0,a.jsx)(o.p,{value:e.major,onChange:s=>G(e.id,"major",s.target.value),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"时间"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(o.p,{type:"month",value:e.start_date,onChange:s=>G(e.id,"start_date",s.target.value),disabled:!P}),(0,a.jsx)(o.p,{type:"month",value:e.end_date,onChange:s=>G(e.id,"end_date",s.target.value),disabled:!P})]})]})]}),P&&(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)(c.$,{variant:"destructive",size:"sm",onClick:()=>{var s;return s=e.id,void B(F.filter(e=>e.id!==s))},children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})})]},e.id))})})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(x.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"工作经历"})]}),P&&(0,a.jsxs)(c.$,{onClick:()=>{$([...O,{id:Date.now().toString(),company:"",position:"",start_date:"",end_date:"",responsibilities:[""]}])},size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"添加"]})]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:O.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"公司名称"}),(0,a.jsx)(o.p,{value:e.company,onChange:s=>Q(e.id,"company",s.target.value),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"职位"}),(0,a.jsx)(o.p,{value:e.position,onChange:s=>Q(e.id,"position",s.target.value),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"开始时间"}),(0,a.jsx)(o.p,{type:"month",value:e.start_date,onChange:s=>Q(e.id,"start_date",s.target.value),disabled:!P})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"结束时间"}),(0,a.jsx)(o.p,{type:"month",value:e.end_date,onChange:s=>Q(e.id,"end_date",s.target.value),disabled:!P})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"工作职责"}),(0,a.jsxs)("div",{className:"space-y-2",children:[e.responsibilities.map((s,r)=>(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(o.p,{value:s,onChange:s=>{let a=[...e.responsibilities];a[r]=s.target.value,Q(e.id,"responsibilities",a)},disabled:!P,placeholder:"描述您的工作职责"}),P&&(0,a.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>{let s=e.responsibilities.filter((e,s)=>s!==r);Q(e.id,"responsibilities",s)},children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]},r)),P&&(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{let s=[...e.responsibilities,""];Q(e.id,"responsibilities",s)},children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"添加职责"]})]})]}),P&&(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)(c.$,{variant:"destructive",size:"sm",onClick:()=>{var s;return s=e.id,void $(O.filter(e=>e.id!==s))},children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})})]},e.id))})})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"技能专长"})]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"编程语言"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:I.programming_languages.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:e},s))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"框架技术"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:I.frameworks.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm",children:e},s))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"数据库"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:I.databases.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm",children:e},s))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"开发工具"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:I.tools.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm",children:e},s))})]})]})})]})]}):(0,a.jsx)(A._A,{delay:400,children:(0,a.jsxs)(x.Zp,{className:"text-center shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,a.jsxs)(x.aR,{className:"bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:[(0,a.jsxs)(x.ZB,{className:"flex items-center justify-center space-x-2 text-xl text-gray-800",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,a.jsx)(g.A,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("span",{children:"上传您的简历"})]}),(0,a.jsx)(x.BT,{className:"text-gray-600",children:"支持PDF、Word格式，AI将自动解析并提取关键信息"})]}),(0,a.jsx)(x.Wu,{className:"space-y-6 p-8",children:R?(0,a.jsxs)("div",{className:"py-12",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(f.A,{className:"h-10 w-10 animate-spin text-blue-600"})}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-800 mb-2",children:"AI正在解析您的简历..."}),(0,a.jsx)("p",{className:"text-gray-600",children:"请稍候，这可能需要几秒钟"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"border-2 border-dashed border-blue-200 rounded-lg p-12 hover:border-blue-400 transition-all duration-300 cursor-pointer bg-gradient-to-br from-blue-50/50 to-white hover:shadow-lg group",onClick:()=>{var e;return null==(e=s.current)?void 0:e.click()},children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg",children:(0,a.jsx)(j.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-800 mb-2",children:"点击上传简历文件"}),(0,a.jsx)("p",{className:"text-gray-600",children:"或拖拽文件到此处"})]}),(0,a.jsx)("input",{ref:s,type:"file",accept:".pdf,.doc,.docx",onChange:M,className:"hidden"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 text-center bg-gray-50 rounded-lg p-3",children:"支持格式：PDF, DOC, DOCX（最大10MB）"})]})})]})})]})})]})})})}},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=r(5155),t=r(2115),l=r(9434);let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});i.displayName="Card";let n=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...t})});n.displayName="CardHeader";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});d.displayName="CardTitle";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",r),...t})});c.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",r),...t})});o.displayName="CardContent",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",r),...t})}).displayName="CardFooter"},6982:(e,s,r)=>{"use strict";r.d(s,{EM:()=>g,ToastProvider:()=>x});var a=r(5155),t=r(2115),l=r(9434),i=r(646),n=r(4861),d=r(5339),c=r(1284),o=r(4416);let u=(0,t.createContext)(void 0);function x(e){let{children:s}=e,[r,l]=(0,t.useState)([]),i=(0,t.useCallback)(e=>{let s=Math.random().toString(36).substr(2,9),r={...e,id:s};l(e=>[...e,r]),setTimeout(()=>{n(s)},e.duration||5e3)},[]),n=(0,t.useCallback)(e=>{l(s=>s.filter(s=>s.id!==e))},[]),d=(0,t.useCallback)((e,s)=>{i({type:"success",title:e,description:s})},[i]),c=(0,t.useCallback)((e,s)=>{i({type:"error",title:e,description:s})},[i]),o=(0,t.useCallback)((e,s)=>{i({type:"warning",title:e,description:s})},[i]),x=(0,t.useCallback)((e,s)=>{i({type:"info",title:e,description:s})},[i]);return(0,a.jsxs)(u.Provider,{value:{toasts:r,addToast:i,removeToast:n,success:d,error:c,warning:o,info:x},children:[s,(0,a.jsx)(m,{toasts:r,onRemove:n})]})}function m(e){let{toasts:s,onRemove:r}=e;return(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.map(e=>(0,a.jsx)(h,{toast:e,onRemove:r},e.id))})}function h(e){let{toast:s,onRemove:r}=e,t={success:i.A,error:n.A,warning:d.A,info:c.A}[s.type];return(0,a.jsx)("div",{className:(0,l.cn)("max-w-sm w-full border rounded-lg p-4 shadow-lg animate-slide-down",{success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"}[s.type]),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(t,{className:(0,l.cn)("h-5 w-5",{success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[s.type])})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:s.title}),s.description&&(0,a.jsx)("p",{className:"mt-1 text-sm opacity-90",children:s.description}),s.action&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("button",{onClick:s.action.onClick,className:"text-sm font-medium underline hover:no-underline",children:s.action.label})})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{onClick:()=>r(s.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})})]})})}let p={success:(e,s)=>{console.log("Success:",e,s)},error:(e,s)=>{console.log("Error:",e,s)},warning:(e,s)=>{console.log("Warning:",e,s)},info:(e,s)=>{console.log("Info:",e,s)}};function g(){let e=(0,t.useContext)(u);return e?{success:e.success,error:e.error,warning:e.warning,info:e.info}:{success:p.success,error:p.error,warning:p.warning,info:p.info}}},7138:(e,s,r)=>{Promise.resolve().then(r.bind(r,5458))},7292:(e,s,r)=>{"use strict";r.d(s,{f:()=>t,j:()=>l});class a{async signUp(e,s,r){await new Promise(e=>setTimeout(e,1e3));let a={id:Date.now().toString(),email:e,user_metadata:r||{}};return this.currentUser=a,this.notifyListeners(),{data:{user:a,session:{access_token:"mock-token"}},error:null}}async signInWithPassword(e,s){if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e&&"demo123"===s){let s={id:"1",email:e,user_metadata:{username:"演示用户"}};return this.currentUser=s,this.notifyListeners(),{data:{user:s,session:{access_token:"mock-token"}},error:null}}return{data:{user:null,session:null},error:{message:"邮箱或密码错误"}}}async signOut(){return this.currentUser=null,this.notifyListeners(),{error:null}}async resetPasswordForEmail(e){return await new Promise(e=>setTimeout(e,1e3)),{error:null}}async updateUser(e){return await new Promise(e=>setTimeout(e,1e3)),this.currentUser&&(this.currentUser={...this.currentUser,...e},this.notifyListeners()),{error:null}}async getSession(){return{data:{session:this.currentUser?{user:this.currentUser}:null}}}onAuthStateChange(e){let s=s=>{e(s?"SIGNED_IN":"SIGNED_OUT",s?{user:s}:null)};return this.listeners.push(s),{data:{subscription:{unsubscribe:()=>{let e=this.listeners.indexOf(s);e>-1&&this.listeners.splice(e,1)}}}}}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}constructor(){this.currentUser=null,this.listeners=[]}}let t=new a,l={auth:t}},9053:(e,s,r)=>{"use strict";r.d(s,{A:()=>n});var a=r(5155),t=r(2115),l=r(5695),i=r(283);function n(e){let{children:s,redirectTo:r="/auth/login"}=e,{user:n,loading:d}=(0,i.A)(),c=(0,l.useRouter)();return((0,t.useEffect)(()=>{d||n||c.push(r)},[n,d,c,r]),d)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):n?(0,a.jsx)(a.Fragment,{children:s}):null}},9434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>l});var a=r(2596),t=r(9688);function l(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}}},e=>{e.O(0,[598,874,278,441,964,358],()=>e(e.s=7138)),_N_E=e.O()}]);