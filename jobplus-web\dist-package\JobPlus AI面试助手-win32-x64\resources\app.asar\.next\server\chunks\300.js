exports.id=300,exports.ids=[300],exports.modules={1628:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},15196:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},29131:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\contexts\\AuthContext.tsx","useAuth");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\contexts\\AuthContext.tsx","AuthProvider")},61135:()=>{},62694:(a,b,c)=>{"use strict";c.d(b,{EM:()=>q,ToastProvider:()=>m});var d=c(60687),e=c(43210),f=c(4780),g=c(5336),h=c(35071),i=c(93613),j=c(96882),k=c(11860);let l=(0,e.createContext)(void 0);function m({children:a}){let[b,c]=(0,e.useState)([]),f=(0,e.useCallback)(a=>{let b=Math.random().toString(36).substr(2,9),d={...a,id:b};c(a=>[...a,d]),setTimeout(()=>{g(b)},a.duration||5e3)},[]),g=(0,e.useCallback)(a=>{c(b=>b.filter(b=>b.id!==a))},[]),h=(0,e.useCallback)((a,b)=>{f({type:"success",title:a,description:b})},[f]),i=(0,e.useCallback)((a,b)=>{f({type:"error",title:a,description:b})},[f]),j=(0,e.useCallback)((a,b)=>{f({type:"warning",title:a,description:b})},[f]),k=(0,e.useCallback)((a,b)=>{f({type:"info",title:a,description:b})},[f]);return(0,d.jsxs)(l.Provider,{value:{toasts:b,addToast:f,removeToast:g,success:h,error:i,warning:j,info:k},children:[a,(0,d.jsx)(n,{toasts:b,onRemove:g})]})}function n({toasts:a,onRemove:b}){return(0,d.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:a.map(a=>(0,d.jsx)(o,{toast:a,onRemove:b},a.id))})}function o({toast:a,onRemove:b}){let c={success:g.A,error:h.A,warning:i.A,info:j.A}[a.type];return(0,d.jsx)("div",{className:(0,f.cn)("max-w-sm w-full border rounded-lg p-4 shadow-lg animate-slide-down",{success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"}[a.type]),children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(c,{className:(0,f.cn)("h-5 w-5",{success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[a.type])})}),(0,d.jsxs)("div",{className:"ml-3 flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:a.title}),a.description&&(0,d.jsx)("p",{className:"mt-1 text-sm opacity-90",children:a.description}),a.action&&(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsx)("button",{onClick:a.action.onClick,className:"text-sm font-medium underline hover:no-underline",children:a.action.label})})]}),(0,d.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,d.jsx)("button",{onClick:()=>b(a.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:(0,d.jsx)(k.A,{className:"h-4 w-4"})})})]})})}let p={success:(a,b)=>{console.log("Success:",a,b)},error:(a,b)=>{console.log("Error:",a,b)},warning:(a,b)=>{console.log("Warning:",a,b)},info:(a,b)=>{console.log("Info:",a,b)}};function q(){let a=(0,e.useContext)(l);return a?{success:a.success,error:a.error,warning:a.warning,info:a.info}:{success:p.success,error:p.error,warning:p.warning,info:p.info}}},63213:(a,b,c)=>{"use strict";c.d(b,{A:()=>h,AuthProvider:()=>i});var d=c(60687),e=c(43210),f=c(94934);let g=(0,e.createContext)({user:null,loading:!0,signOut:async()=>{}}),h=()=>{let a=(0,e.useContext)(g);if(!a)throw Error("useAuth must be used within an AuthProvider");return a},i=({children:a})=>{let[b,c]=(0,e.useState)(null),[h,i]=(0,e.useState)(!0);(0,e.useEffect)(()=>{(async()=>{let{data:{session:a}}=await f.j.auth.getSession();c(a?.user??null),i(!1)})();let{data:{subscription:a}}=f.j.auth.onAuthStateChange(async(a,b)=>{c(b?.user??null),i(!1)});return()=>a.unsubscribe()},[]);let j=async()=>{await f.j.auth.signOut()};return(0,d.jsx)(g.Provider,{value:{user:b,loading:h,signOut:j},children:a})}},65413:(a,b,c)=>{Promise.resolve().then(c.bind(c,90896)),Promise.resolve().then(c.bind(c,29131))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},75685:(a,b,c)=>{Promise.resolve().then(c.bind(c,62694)),Promise.resolve().then(c.bind(c,63213))},90896:(a,b,c)=>{"use strict";c.d(b,{ToastProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\toast.tsx","useToast");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\toast.tsx","ToastProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\toast.tsx","toast"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useToastActions() from the server but useToastActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\components\\ui\\toast.tsx","useToastActions")},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(51194),f=c.n(e);c(61135);var g=c(29131),h=c(90896);let i={title:"JobPlus - AI面试助手",description:"JobPlus是一款智能面试辅助工具，通过AI实时分析面试问题，为求职者提供专业的回答思路和要点提示，帮助您在面试中更加自信从容。",keywords:["面试助手","AI面试","求职","面试准备","职业发展","面试练习","求职辅导"],authors:[{name:"JobPlus Team"}],creator:"JobPlus",publisher:"JobPlus",robots:"index, follow",openGraph:{title:"JobPlus - AI面试助手",description:"智能面试辅助平台，帮助您在面试中表现更出色",type:"website",locale:"zh_CN"},twitter:{card:"summary_large_image",title:"JobPlus - AI面试助手",description:"智能面试辅助平台，帮助您在面试中表现更出色"},icons:{icon:"/favicon.ico",apple:"/apple-touch-icon.png"},manifest:"/manifest.json"};function j({children:a}){return(0,d.jsxs)("html",{lang:"zh-CN",className:f().variable,children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("meta",{name:"theme-color",content:"#2563eb"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-title",content:"JobPlus"}),(0,d.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,d.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"msapplication-TileColor",content:"#2563eb"}),(0,d.jsx)("meta",{name:"msapplication-tap-highlight",content:"no"})]}),(0,d.jsx)("body",{className:"font-sans antialiased min-h-screen bg-background",children:(0,d.jsx)(h.ToastProvider,{children:(0,d.jsx)(g.AuthProvider,{children:a})})})]})}},94934:(a,b,c)=>{"use strict";c.d(b,{f:()=>e,j:()=>f});class d{async signUp(a,b,c){await new Promise(a=>setTimeout(a,1e3));let d={id:Date.now().toString(),email:a,user_metadata:c||{}};return this.currentUser=d,this.notifyListeners(),{data:{user:d,session:{access_token:"mock-token"}},error:null}}async signInWithPassword(a,b){if(await new Promise(a=>setTimeout(a,1e3)),"<EMAIL>"===a&&"demo123"===b){let b={id:"1",email:a,user_metadata:{username:"演示用户"}};return this.currentUser=b,this.notifyListeners(),{data:{user:b,session:{access_token:"mock-token"}},error:null}}return{data:{user:null,session:null},error:{message:"邮箱或密码错误"}}}async signOut(){return this.currentUser=null,this.notifyListeners(),{error:null}}async resetPasswordForEmail(a){return await new Promise(a=>setTimeout(a,1e3)),{error:null}}async updateUser(a){return await new Promise(a=>setTimeout(a,1e3)),this.currentUser&&(this.currentUser={...this.currentUser,...a},this.notifyListeners()),{error:null}}async getSession(){return{data:{session:this.currentUser?{user:this.currentUser}:null}}}onAuthStateChange(a){let b=b=>{a(b?"SIGNED_IN":"SIGNED_OUT",b?{user:b}:null)};return this.listeners.push(b),{data:{subscription:{unsubscribe:()=>{let a=this.listeners.indexOf(b);a>-1&&this.listeners.splice(a,1)}}}}}notifyListeners(){this.listeners.forEach(a=>a(this.currentUser))}constructor(){this.currentUser=null,this.listeners=[]}}let e=new d,f={auth:e}}};