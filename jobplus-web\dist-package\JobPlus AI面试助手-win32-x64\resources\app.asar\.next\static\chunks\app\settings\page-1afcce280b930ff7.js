(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[662],{283:(e,s,r)=>{"use strict";r.d(s,{A:()=>i,AuthProvider:()=>d});var t=r(5155),a=r(2115),n=r(7292);let l=(0,a.createContext)({user:null,loading:!0,signOut:async()=>{}}),i=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},d=e=>{let{children:s}=e,[r,i]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{var e;let{data:{session:s}}=await n.j.auth.getSession();i(null!=(e=null==s?void 0:s.user)?e:null),c(!1)})();let{data:{subscription:e}}=n.j.auth.onAuthStateChange(async(e,s)=>{var r;i(null!=(r=null==s?void 0:s.user)?r:null),c(!1)});return()=>e.unsubscribe()},[]);let o=async()=>{await n.j.auth.signOut()};return(0,t.jsx)(l.Provider,{value:{user:r,loading:d,signOut:o},children:s})}},285:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var t=r(5155),a=r(2115),n=r(4624),l=r(2085),i=r(9434);let d=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,s)=>{let{className:r,variant:a,size:l,asChild:c=!1,...o}=e,u=c?n.DX:"button";return(0,t.jsx)(u,{className:(0,i.cn)(d({variant:a,size:l,className:r})),ref:s,...o})});c.displayName="Button"},1007:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2085:(e,s,r)=>{"use strict";r.d(s,{F:()=>l});var t=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=t.$,l=(e,s)=>r=>{var t;if((null==s?void 0:s.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=s,d=Object.keys(l).map(e=>{let s=null==r?void 0:r[e],t=null==i?void 0:i[e];if(null===s)return null;let n=a(s)||a(t);return l[e][n]}),c=r&&Object.entries(r).reduce((e,s)=>{let[r,t]=s;return void 0===t||(e[r]=t),e},{});return n(e,d,null==s||null==(t=s.compoundVariants)?void 0:t.reduce((e,s)=>{let{class:r,className:t,...a}=s;return Object.entries(a).every(e=>{let[s,r]=e;return Array.isArray(r)?r.includes({...i,...c}[s]):({...i,...c})[s]===r})?[...e,r,t]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var t=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,s)=>{let{className:r,type:a,...l}=e;return(0,t.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...l})});l.displayName="Input"},2556:(e,s,r)=>{Promise.resolve().then(r.bind(r,4175))},4175:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(5155),a=r(2115),n=r(6874),l=r.n(n),i=r(283),d=r(9053),c=r(285),o=r(2523),u=r(5057),h=r(6695),m=r(5169),x=r(9376),f=r(1007),p=r(4229),y=r(9946);let v=(0,y.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),j=(0,y.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),g=(0,y.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);function b(){var e;let{user:s,signOut:r}=(0,i.A)(),[n,y]=(0,a.useState)(!1),[b,N]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[A,R]=(0,a.useState)({username:(null==s||null==(e=s.user_metadata)?void 0:e.username)||"",email:(null==s?void 0:s.email)||"",phone:"",city:""}),[C,P]=(0,a.useState)({emailNotifications:!0,interviewReminders:!0,weeklyReports:!1}),S=async e=>{e.preventDefault(),y(!0),k(""),N("");try{N("个人信息更新成功！")}catch(e){k("更新失败，请稍后重试")}finally{y(!1)}},M=async()=>{y(!0),k(""),N("");try{N("通知设置更新成功！")}catch(e){k("更新失败，请稍后重试")}finally{y(!1)}};return(0,t.jsx)(d.A,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white border-b",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l(),{href:"/dashboard",children:(0,t.jsx)(c.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"JobPlus"})]})]}),(0,t.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900",children:"控制台"}),(0,t.jsx)("span",{className:"text-blue-600 font-medium",children:"设置"})]})]})}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"账户设置"}),b&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-md",children:(0,t.jsx)("p",{className:"text-green-800",children:b})}),w&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsx)("p",{className:"text-red-800",children:w})}),(0,t.jsxs)("div",{className:"grid gap-6",children:[(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsxs)(h.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"个人信息"})]}),(0,t.jsx)(h.BT,{children:"管理您的个人资料和联系信息"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"username",children:"用户名"}),(0,t.jsx)(o.p,{id:"username",value:A.username,onChange:e=>R(s=>({...s,username:e.target.value})),placeholder:"请输入用户名"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"邮箱地址"}),(0,t.jsx)(o.p,{id:"email",type:"email",value:A.email,disabled:!0,className:"bg-gray-50"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"phone",children:"手机号码"}),(0,t.jsx)(o.p,{id:"phone",value:A.phone,onChange:e=>R(s=>({...s,phone:e.target.value})),placeholder:"请输入手机号码"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"city",children:"所在城市"}),(0,t.jsx)(o.p,{id:"city",value:A.city,onChange:e=>R(s=>({...s,city:e.target.value})),placeholder:"请输入所在城市"})]})]}),(0,t.jsxs)(c.$,{type:"submit",disabled:n,children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),n?"保存中...":"保存更改"]})]})})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsxs)(h.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(v,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"安全设置"})]}),(0,t.jsx)(h.BT,{children:"管理您的密码和安全选项"})]}),(0,t.jsxs)(h.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"密码"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"上次更新：30天前"})]}),(0,t.jsx)(l(),{href:"/auth/change-password",children:(0,t.jsxs)(c.$,{variant:"outline",children:[(0,t.jsx)(j,{className:"h-4 w-4 mr-2"}),"修改密码"]})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"两步验证"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"增强账户安全性"})]}),(0,t.jsx)(c.$,{variant:"outline",disabled:!0,children:"即将推出"})]})]})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsxs)(h.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(g,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"通知设置"})]}),(0,t.jsx)(h.BT,{children:"选择您希望接收的通知类型"})]}),(0,t.jsxs)(h.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"邮件通知"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"接收重要更新和提醒"})]}),(0,t.jsx)("input",{type:"checkbox",checked:C.emailNotifications,onChange:e=>P(s=>({...s,emailNotifications:e.target.checked})),className:"h-4 w-4 text-blue-600 rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"面试提醒"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"面试前的提醒通知"})]}),(0,t.jsx)("input",{type:"checkbox",checked:C.interviewReminders,onChange:e=>P(s=>({...s,interviewReminders:e.target.checked})),className:"h-4 w-4 text-blue-600 rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:"周报"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"每周使用情况总结"})]}),(0,t.jsx)("input",{type:"checkbox",checked:C.weeklyReports,onChange:e=>P(s=>({...s,weeklyReports:e.target.checked})),className:"h-4 w-4 text-blue-600 rounded"})]})]}),(0,t.jsxs)(c.$,{onClick:M,disabled:n,children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"保存通知设置"]})]})]}),(0,t.jsxs)(h.Zp,{className:"border-red-200",children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{className:"text-red-600",children:"危险操作"}),(0,t.jsx)(h.BT,{children:"这些操作不可逆，请谨慎操作"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsx)(c.$,{variant:"destructive",onClick:r,children:"退出登录"})})]})]})]})})]})})}},4229:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4624:(e,s,r)=>{"use strict";r.d(s,{DX:()=>i,TL:()=>l});var t=r(2115);function a(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}var n=r(5155);function l(e){let s=function(e){let s=t.forwardRef((e,s)=>{let{children:r,...n}=e;if(t.isValidElement(r)){var l;let e,i,d=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,s){let r={...s};for(let t in s){let a=e[t],n=s[t];/^on[A-Z]/.test(t)?a&&n?r[t]=(...e)=>{let s=n(...e);return a(...e),s}:a&&(r[t]=a):"style"===t?r[t]={...a,...n}:"className"===t&&(r[t]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==t.Fragment&&(c.ref=s?function(...e){return s=>{let r=!1,t=e.map(e=>{let t=a(e,s);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let s=0;s<t.length;s++){let r=t[s];"function"==typeof r?r():a(e[s],null)}}}}(s,d):d),t.cloneElement(r,c)}return t.Children.count(r)>1?t.Children.only(null):null});return s.displayName=`${e}.SlotClone`,s}(e),r=t.forwardRef((e,r)=>{let{children:a,...l}=e,i=t.Children.toArray(a),d=i.find(c);if(d){let e=d.props.children,a=i.map(s=>s!==d?s:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,n.jsx)(s,{...l,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,a):null})}return(0,n.jsx)(s,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),d=Symbol("radix.slottable");function c(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}},5057:(e,s,r)=>{"use strict";r.d(s,{J:()=>c});var t=r(5155),a=r(2115),n=r(7073),l=r(2085),i=r(9434);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(n.b,{ref:s,className:(0,i.cn)(d(),r),...a})});c.displayName=n.b.displayName},5169:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>i});var t=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},7073:(e,s,r)=>{"use strict";r.d(s,{b:()=>d});var t=r(2115);r(7650);var a=r(4624),n=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,a.TL)(`Primitive.${s}`),l=t.forwardRef((e,t)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:s,{...l,ref:t})});return l.displayName=`Primitive.${s}`,{...e,[s]:l}},{}),i=t.forwardRef((e,s)=>(0,n.jsx)(l.label,{...e,ref:s,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var d=i},7292:(e,s,r)=>{"use strict";r.d(s,{f:()=>a,j:()=>n});class t{async signUp(e,s,r){await new Promise(e=>setTimeout(e,1e3));let t={id:Date.now().toString(),email:e,user_metadata:r||{}};return this.currentUser=t,this.notifyListeners(),{data:{user:t,session:{access_token:"mock-token"}},error:null}}async signInWithPassword(e,s){if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e&&"demo123"===s){let s={id:"1",email:e,user_metadata:{username:"演示用户"}};return this.currentUser=s,this.notifyListeners(),{data:{user:s,session:{access_token:"mock-token"}},error:null}}return{data:{user:null,session:null},error:{message:"邮箱或密码错误"}}}async signOut(){return this.currentUser=null,this.notifyListeners(),{error:null}}async resetPasswordForEmail(e){return await new Promise(e=>setTimeout(e,1e3)),{error:null}}async updateUser(e){return await new Promise(e=>setTimeout(e,1e3)),this.currentUser&&(this.currentUser={...this.currentUser,...e},this.notifyListeners()),{error:null}}async getSession(){return{data:{session:this.currentUser?{user:this.currentUser}:null}}}onAuthStateChange(e){let s=s=>{e(s?"SIGNED_IN":"SIGNED_OUT",s?{user:s}:null)};return this.listeners.push(s),{data:{subscription:{unsubscribe:()=>{let e=this.listeners.indexOf(s);e>-1&&this.listeners.splice(e,1)}}}}}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}constructor(){this.currentUser=null,this.listeners=[]}}let a=new t,n={auth:a}},9053:(e,s,r)=>{"use strict";r.d(s,{A:()=>i});var t=r(5155),a=r(2115),n=r(5695),l=r(283);function i(e){let{children:s,redirectTo:r="/auth/login"}=e,{user:i,loading:d}=(0,l.A)(),c=(0,n.useRouter)();return((0,a.useEffect)(()=>{d||i||c.push(r)},[i,d,c,r]),d)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):i?(0,t.jsx)(t.Fragment,{children:s}):null}},9376:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]])},9434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(2596),a=r(9688);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}}},e=>{e.O(0,[598,874,441,964,358],()=>e(e.s=2556)),_N_E=e.O()}]);