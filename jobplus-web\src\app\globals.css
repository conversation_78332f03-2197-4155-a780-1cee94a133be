@import "tailwindcss";

:root {
  /* Base colors */
  --background: #ffffff;
  --foreground: #171717;

  /* Brand colors - Professional blue gradient */
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;

  /* Secondary colors */
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;

  /* Neutral colors */
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;

  /* Status colors */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --info: #3b82f6;
  --info-foreground: #ffffff;

  /* UI elements */
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;
  --card: #ffffff;
  --card-foreground: #171717;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  --gradient-secondary: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
}

@theme inline {
  /* Base colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Brand colors */
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-light: var(--primary-light);
  --color-primary-dark: var(--primary-dark);

  /* Secondary colors */
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  /* Neutral colors */
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  /* Status colors */
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);

  /* UI elements */
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  /* Typography */
  --font-sans: ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Base colors */
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Brand colors */
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --primary-light: #60a5fa;
    --primary-dark: #1d4ed8;

    /* Secondary colors */
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;

    /* Neutral colors */
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f8fafc;

    /* Status colors */
    --destructive: #dc2626;
    --destructive-foreground: #f8fafc;
    --success: #059669;
    --success-foreground: #ffffff;
    --warning: #d97706;
    --warning-foreground: #ffffff;
    --info: #3b82f6;
    --info-foreground: #ffffff;

    /* UI elements */
    --border: #1e293b;
    --input: #1e293b;
    --ring: #3b82f6;
    --card: #111827;
    --card-foreground: #ededed;

    /* Shadows for dark mode */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  }
}

/* Global styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: var(--radius);
  opacity: 0.5;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
  opacity: 0.8;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}

/* Utility classes */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-success {
  background: var(--gradient-success);
}

.gradient-warning {
  background: var(--gradient-warning);
}

.shadow-custom {
  box-shadow: var(--shadow);
}

.shadow-custom-md {
  box-shadow: var(--shadow-md);
}

.shadow-custom-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-custom-xl {
  box-shadow: var(--shadow-xl);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) ease-out;
}

.animate-slide-up {
  animation: slideUp var(--duration-normal) ease-out;
}

.animate-slide-down {
  animation: slideDown var(--duration-normal) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--duration-fast) ease-out;
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive typography */
@media (max-width: 640px) {
  body {
    font-size: 14px;
  }
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
