const { spawn } = require('child_process');
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

let electronProcess = null;

function startElectron() {
  if (electronProcess) {
    electronProcess.kill();
  }
  
  electronProcess = spawn('electron', ['.'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'development'
    }
  });
  
  electronProcess.on('close', () => {
    process.exit();
  });
}

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(3000, (err) => {
    if (err) throw err;
    console.log('> Ready on http://localhost:3000');
    
    // Start Electron after Next.js is ready
    setTimeout(startElectron, 2000);
  });
});

process.on('SIGTERM', () => {
  if (electronProcess) {
    electronProcess.kill();
  }
  process.exit();
});

process.on('SIGINT', () => {
  if (electronProcess) {
    electronProcess.kill();
  }
  process.exit();
});
