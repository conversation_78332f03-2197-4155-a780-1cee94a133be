interface MessageBoxOptions {
  type?: 'none' | 'info' | 'error' | 'question' | 'warning';
  title?: string;
  message: string;
  detail?: string;
  buttons?: string[];
}

interface MessageBoxResult {
  response: number;
  checkboxChecked?: boolean;
}

interface ElectronAPI {
  getVersion: () => Promise<string>;
  showMessageBox: (options: MessageBoxOptions) => Promise<MessageBoxResult>;
  platform: string;
  isDev: boolean;
  requestMediaPermissions: () => Promise<boolean>;
  selectFile: () => Promise<string | null>;
  saveFile: (data: unknown, filename: string) => Promise<boolean>;
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
  toggleFullscreen: () => Promise<boolean>;
  isFullscreen: () => Promise<boolean>;
  getTheme: () => string;
  onThemeChange: (callback: (theme: string) => void) => () => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    isElectron: boolean;
    electronVersion: string;
    nodeVersion: string;
    chromeVersion: string;
  }
}

export {};
