import { useState, useEffect } from 'react';

interface ElectronInfo {
  isElectron: boolean;
  platform?: string;
  isDev?: boolean;
  version?: string;
}

export function useElectron(): ElectronInfo {
  const [electronInfo, setElectronInfo] = useState<ElectronInfo>({
    isElectron: false
  });

  useEffect(() => {
    // Check if we're running in Electron
    const isElectron = typeof window !== 'undefined' && window.isElectron;
    
    if (isElectron && window.electronAPI) {
      window.electronAPI.getVersion().then(version => {
        setElectronInfo({
          isElectron: true,
          platform: window.electronAPI.platform,
          isDev: window.electronAPI.isDev,
          version
        });
      }).catch(() => {
        setElectronInfo({
          isElectron: true,
          platform: window.electronAPI.platform,
          isDev: window.electronAPI.isDev
        });
      });
    } else {
      setElectronInfo({ isElectron: false });
    }
  }, []);

  return electronInfo;
}

export function useElectronAPI() {
  const { isElectron } = useElectron();
  
  if (!isElectron || typeof window === 'undefined' || !window.electronAPI) {
    return null;
  }
  
  return window.electronAPI;
}
