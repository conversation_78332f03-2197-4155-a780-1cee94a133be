import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/components/ui/toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
});

export const metadata: Metadata = {
  title: "JobPlus - AI面试助手",
  description: "JobPlus是一款智能面试辅助工具，通过AI实时分析面试问题，为求职者提供专业的回答思路和要点提示，帮助您在面试中更加自信从容。",
  keywords: ["面试助手", "AI面试", "求职", "面试准备", "职业发展", "面试练习", "求职辅导"],
  authors: [{ name: "JobPlus Team" }],
  creator: "JobPlus",
  publisher: "JobPlus",
  robots: "index, follow",
  openGraph: {
    title: "JobPlus - AI面试助手",
    description: "智能面试辅助平台，帮助您在面试中表现更出色",
    type: "website",
    locale: "zh_CN",
  },
  twitter: {
    card: "summary_large_image",
    title: "JobPlus - AI面试助手",
    description: "智能面试辅助平台，帮助您在面试中表现更出色",
  },
  icons: {
    icon: "/favicon.ico",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/manifest.json",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" className={inter.variable}>
      <head>
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="JobPlus" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#2563eb" />
        <meta name="msapplication-tap-highlight" content="no" />
      </head>
      <body className="font-sans antialiased min-h-screen bg-background">
        <ToastProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ToastProvider>
      </body>
    </html>
  );
}
