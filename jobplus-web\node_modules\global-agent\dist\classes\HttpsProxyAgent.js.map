{"version": 3, "sources": ["../../src/classes/HttpsProxyAgent.js"], "names": ["HttpsProxyAgent", "Agent", "constructor", "args", "protocol", "defaultPort", "createConnection", "configuration", "callback", "socket", "net", "connect", "proxy", "port", "hostname", "on", "error", "once", "secureSocket", "tls", "connectMessage", "host", "authorization", "<PERSON><PERSON><PERSON>", "from", "toString", "write"], "mappings": ";;;;;;;AAEA;;AACA;;AAKA;;;;AAEA,MAAMA,eAAN,SAA8BC,cAA9B,CAAoC;AAClC;AACAC,EAAAA,WAAW,CAAE,GAAGC,IAAL,EAAc;AACvB,UAAM,GAAGA,IAAT;AAEA,SAAKC,QAAL,GAAgB,QAAhB;AACA,SAAKC,WAAL,GAAmB,GAAnB;AACD;;AAEDC,EAAAA,gBAAgB,CAAEC,aAAF,EAA8CC,QAA9C,EAAgF;AAC9F,UAAMC,MAAM,GAAGC,aAAIC,OAAJ,CACbJ,aAAa,CAACK,KAAd,CAAoBC,IADP,EAEbN,aAAa,CAACK,KAAd,CAAoBE,QAFP,CAAf;;AAKAL,IAAAA,MAAM,CAACM,EAAP,CAAU,OAAV,EAAoBC,KAAD,IAAW;AAC5BR,MAAAA,QAAQ,CAACQ,KAAD,CAAR;AACD,KAFD;AAIAP,IAAAA,MAAM,CAACQ,IAAP,CAAY,MAAZ,EAAoB,MAAM;AACxB,YAAMC,YAAY,GAAGC,aAAIR,OAAJ,CAAY,EAC/B,GAAGJ,aAAa,CAACY,GADc;AAE/BV,QAAAA;AAF+B,OAAZ,CAArB;;AAKAD,MAAAA,QAAQ,CAAC,IAAD,EAAOU,YAAP,CAAR;AACD,KAPD;AASA,QAAIE,cAAc,GAAG,EAArB;AAEAA,IAAAA,cAAc,IAAI,aAAab,aAAa,CAACc,IAA3B,GAAkC,GAAlC,GAAwCd,aAAa,CAACM,IAAtD,GAA6D,eAA/E;AACAO,IAAAA,cAAc,IAAI,WAAWb,aAAa,CAACc,IAAzB,GAAgC,GAAhC,GAAsCd,aAAa,CAACM,IAApD,GAA2D,MAA7E;;AAEA,QAAIN,aAAa,CAACK,KAAd,CAAoBU,aAAxB,EAAuC;AACrCF,MAAAA,cAAc,IAAI,gCAAgCG,MAAM,CAACC,IAAP,CAAYjB,aAAa,CAACK,KAAd,CAAoBU,aAAhC,EAA+CG,QAA/C,CAAwD,QAAxD,CAAhC,GAAoG,MAAtH;AACD;;AAEDL,IAAAA,cAAc,IAAI,MAAlB;AAEAX,IAAAA,MAAM,CAACiB,KAAP,CAAaN,cAAb;AACD;;AAxCiC;;eA2CrBpB,e", "sourcesContent": ["// @flow\n\nimport net from 'net';\nimport tls from 'tls';\nimport type {\n  ConnectionCallbackType,\n  ConnectionConfigurationType,\n} from '../types';\nimport Agent from './Agent';\n\nclass HttpsProxyAgent extends Agent {\n  // eslint-disable-next-line unicorn/prevent-abbreviations\n  constructor (...args: *) {\n    super(...args);\n\n    this.protocol = 'https:';\n    this.defaultPort = 443;\n  }\n\n  createConnection (configuration: ConnectionConfigurationType, callback: ConnectionCallbackType) {\n    const socket = net.connect(\n      configuration.proxy.port,\n      configuration.proxy.hostname,\n    );\n\n    socket.on('error', (error) => {\n      callback(error);\n    });\n\n    socket.once('data', () => {\n      const secureSocket = tls.connect({\n        ...configuration.tls,\n        socket,\n      });\n\n      callback(null, secureSocket);\n    });\n\n    let connectMessage = '';\n\n    connectMessage += 'CONNECT ' + configuration.host + ':' + configuration.port + ' HTTP/1.1\\r\\n';\n    connectMessage += 'Host: ' + configuration.host + ':' + configuration.port + '\\r\\n';\n\n    if (configuration.proxy.authorization) {\n      connectMessage += 'Proxy-Authorization: Basic ' + Buffer.from(configuration.proxy.authorization).toString('base64') + '\\r\\n';\n    }\n\n    connectMessage += '\\r\\n';\n\n    socket.write(connectMessage);\n  }\n}\n\nexport default HttpsProxyAgent;\n"], "file": "HttpsProxyAgent.js"}