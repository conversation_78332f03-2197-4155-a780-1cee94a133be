(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[983],{688:(e,s,t)=>{"use strict";t.d(s,{Oq:()=>c,Z_:()=>n,_A:()=>i});var a=t(5155),r=t(2115),l=t(9434);function n(e){let{children:s,className:t,delay:n=0}=e,[i,c]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=setTimeout(()=>{c(!0)},n);return()=>clearTimeout(e)},[n]),(0,a.jsx)("div",{className:(0,l.cn)("transition-all duration-500 ease-out",i?"opacity-100 translate-y-0":"opacity-0 translate-y-4",t),children:s})}function i(e){let{children:s,direction:t="up",delay:n=0,duration:i=500,className:c}=e,[o,d]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=setTimeout(()=>{d(!0)},n);return()=>clearTimeout(e)},[n]),(0,a.jsx)("div",{className:(0,l.cn)("transition-all ease-out",o?"opacity-100 translate-x-0 translate-y-0":"opacity-0 ".concat({up:"translate-y-4",down:"-translate-y-4",left:"translate-x-4",right:"-translate-x-4"}[t]),c),style:{transitionDuration:"".concat(i,"ms")},children:s})}function c(e){let{children:s,delay:t=0,staggerDelay:l=100,className:n}=e,c=r.Children.toArray(s);return(0,a.jsx)("div",{className:n,children:c.map((e,s)=>(0,a.jsx)(i,{delay:t+s*l,children:e},s))})}},1284:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1668:(e,s,t)=>{Promise.resolve().then(t.bind(t,6117))},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6117:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(5155),r=t(2115),l=t(6874),n=t.n(l),i=t(5695),c=t(285),o=t(2523),d=t(5057),u=t(6695),m=t(646),x=t(9376),h=t(8749),p=t(2657),f=t(7292),y=t(688),j=t(6982);function g(){let[e,s]=(0,r.useState)({username:"",email:"",password:"",confirmPassword:""}),[t,l]=(0,r.useState)(!1),[g,b]=(0,r.useState)(!1),[w,N]=(0,r.useState)(!1),[v,k]=(0,r.useState)(""),[A,C]=(0,r.useState)(!1),S=(0,i.useRouter)();(0,j.EM)();let P=e=>{let{name:t,value:a}=e.target;s(e=>({...e,[t]:a}))},_=async s=>{if(s.preventDefault(),k(""),e.password!==e.confirmPassword?(k("两次输入的密码不一致"),!1):!(e.password.length<6)||(k("密码长度至少为6位"),!1)){N(!0);try{let{data:s,error:t}=await f.j.auth.signUp(e.email,e.password,{username:e.username});t?k(t.message):C(!0)}catch(e){k("注册失败，请稍后重试")}finally{N(!1)}}};return A?(0,a.jsx)(y.Z_,{children:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)(u.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsx)(m.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,a.jsx)(u.ZB,{className:"text-2xl",children:"注册成功！"}),(0,a.jsx)(u.BT,{children:"我们已向您的邮箱发送了验证链接，请查收邮件并点击链接完成验证。"})]}),(0,a.jsx)(u.Wu,{children:(0,a.jsx)(c.$,{onClick:()=>S.push("/auth/login"),className:"w-full",children:"前往登录"})})]})})}):(0,a.jsx)(y.Z_,{children:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)(u.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"JobPlus"})]}),(0,a.jsx)(u.ZB,{className:"text-2xl",children:"创建账户"}),(0,a.jsx)(u.BT,{children:"加入JobPlus，开始您的AI面试助手之旅"})]}),(0,a.jsxs)(u.Wu,{children:[(0,a.jsxs)("form",{onSubmit:_,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"username",children:"用户名"}),(0,a.jsx)(o.p,{id:"username",name:"username",type:"text",placeholder:"请输入用户名",value:e.username,onChange:P,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"email",children:"邮箱地址"}),(0,a.jsx)(o.p,{id:"email",name:"email",type:"email",placeholder:"请输入您的邮箱",value:e.email,onChange:P,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"password",children:"密码"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"password",name:"password",type:t?"text":"password",placeholder:"请输入密码（至少6位）",value:e.password,onChange:P,required:!0}),(0,a.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>l(!t),children:t?(0,a.jsx)(h.A,{className:"h-4 w-4"}):(0,a.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"confirmPassword",children:"确认密码"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"confirmPassword",name:"confirmPassword",type:g?"text":"password",placeholder:"请再次输入密码",value:e.confirmPassword,onChange:P,required:!0}),(0,a.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>b(!g),children:g?(0,a.jsx)(h.A,{className:"h-4 w-4"}):(0,a.jsx)(p.A,{className:"h-4 w-4"})})]})]}),v&&(0,a.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded-md",children:v}),(0,a.jsx)(c.$,{type:"submit",className:"w-full",disabled:w,children:w?"注册中...":"创建账户"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["已有账户？"," ",(0,a.jsx)(n(),{href:"/auth/login",className:"text-blue-600 hover:underline",children:"立即登录"})]})})]})]})})})}},6982:(e,s,t)=>{"use strict";t.d(s,{EM:()=>f,ToastProvider:()=>m});var a=t(5155),r=t(2115),l=t(9434),n=t(646),i=t(4861),c=t(5339),o=t(1284),d=t(4416);let u=(0,r.createContext)(void 0);function m(e){let{children:s}=e,[t,l]=(0,r.useState)([]),n=(0,r.useCallback)(e=>{let s=Math.random().toString(36).substr(2,9),t={...e,id:s};l(e=>[...e,t]),setTimeout(()=>{i(s)},e.duration||5e3)},[]),i=(0,r.useCallback)(e=>{l(s=>s.filter(s=>s.id!==e))},[]),c=(0,r.useCallback)((e,s)=>{n({type:"success",title:e,description:s})},[n]),o=(0,r.useCallback)((e,s)=>{n({type:"error",title:e,description:s})},[n]),d=(0,r.useCallback)((e,s)=>{n({type:"warning",title:e,description:s})},[n]),m=(0,r.useCallback)((e,s)=>{n({type:"info",title:e,description:s})},[n]);return(0,a.jsxs)(u.Provider,{value:{toasts:t,addToast:n,removeToast:i,success:c,error:o,warning:d,info:m},children:[s,(0,a.jsx)(x,{toasts:t,onRemove:i})]})}function x(e){let{toasts:s,onRemove:t}=e;return(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.map(e=>(0,a.jsx)(h,{toast:e,onRemove:t},e.id))})}function h(e){let{toast:s,onRemove:t}=e,r={success:n.A,error:i.A,warning:c.A,info:o.A}[s.type];return(0,a.jsx)("div",{className:(0,l.cn)("max-w-sm w-full border rounded-lg p-4 shadow-lg animate-slide-down",{success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"}[s.type]),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(r,{className:(0,l.cn)("h-5 w-5",{success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[s.type])})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:s.title}),s.description&&(0,a.jsx)("p",{className:"mt-1 text-sm opacity-90",children:s.description}),s.action&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("button",{onClick:s.action.onClick,className:"text-sm font-medium underline hover:no-underline",children:s.action.label})})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{onClick:()=>t(s.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})})]})})}let p={success:(e,s)=>{console.log("Success:",e,s)},error:(e,s)=>{console.log("Error:",e,s)},warning:(e,s)=>{console.log("Warning:",e,s)},info:(e,s)=>{console.log("Info:",e,s)}};function f(){let e=(0,r.useContext)(u);return e?{success:e.success,error:e.error,warning:e.warning,info:e.info}:{success:p.success,error:p.error,warning:p.warning,info:p.info}}}},e=>{e.O(0,[598,874,196,441,964,358],()=>e(e.s=1668)),_N_E=e.O()}]);