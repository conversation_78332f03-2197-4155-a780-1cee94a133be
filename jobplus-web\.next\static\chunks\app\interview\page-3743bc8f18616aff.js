(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[66],{1880:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>_});var i=t(5155),a=t(2115),r=t(6874),n=t.n(r),l=t(283),c=t(9053),o=t(285),d=t(2523),u=t(5057),h=t(6695);class m{async initialize(){try{return this.stream=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:this.config.sampleRate,channelCount:this.config.channels,echoCancellation:this.config.echoCancellation,noiseSuppression:this.config.noiseSuppression}}),this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.analyser=this.audioContext.createAnalyser(),this.microphone=this.audioContext.createMediaStreamSource(this.stream),this.analyser.fftSize=256,this.dataArray=new Uint8Array(this.analyser.frequencyBinCount),this.microphone.connect(this.analyser),this.mediaRecorder=new MediaRecorder(this.stream,{mimeType:this.getSupportedMimeType()}),this.setupMediaRecorderEvents(),!0}catch(e){return console.error("Failed to initialize audio processor:",e),!1}}getSupportedMimeType(){for(let e of["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/wav"])if(MediaRecorder.isTypeSupported(e))return e;return"audio/webm"}setupMediaRecorderEvents(){this.mediaRecorder&&(this.mediaRecorder.ondataavailable=e=>{e.data.size>0&&this.audioChunks.push(e.data)},this.mediaRecorder.onstop=()=>{var e;let s=new Blob(this.audioChunks,{type:(null==(e=this.mediaRecorder)?void 0:e.mimeType)||"audio/webm"});this.processAudioBlob(s),this.audioChunks=[]})}startRecording(){if(!this.mediaRecorder||this.isRecording)return!1;try{return this.audioChunks=[],this.mediaRecorder.start(1e3),this.isRecording=!0,this.startAudioAnalysis(),!0}catch(e){return console.error("Failed to start recording:",e),!1}}stopRecording(){if(!this.mediaRecorder||!this.isRecording)return!1;try{return this.mediaRecorder.stop(),this.isRecording=!1,this.stopAudioAnalysis(),!0}catch(e){return console.error("Failed to stop recording:",e),!1}}startAudioAnalysis(){if(!this.analyser||!this.dataArray)return;let e=()=>{if(!this.isRecording)return;this.analyser.getByteFrequencyData(this.dataArray);let s=0;for(let e=0;e<this.dataArray.length;e++)s+=this.dataArray[e]*this.dataArray[e];let t=Math.sqrt(s/this.dataArray.length)/255,i=t>.1,a=0,r=0;for(let e=0;e<this.dataArray.length;e++)this.dataArray[e]>r&&(r=this.dataArray[e],a=e);let n={volume:100*t,frequency:a*this.config.sampleRate/(2*this.analyser.fftSize),speechDetected:i,silenceDuration:i?0:Date.now()};this.onDataCallback&&this.onDataCallback(n),requestAnimationFrame(e)};e()}stopAudioAnalysis(){}async processAudioBlob(e){try{let s=await this.mockSpeechRecognition(e);this.onSpeechCallback&&s&&this.onSpeechCallback(s)}catch(e){console.error("Failed to process audio blob:",e)}}async mockSpeechRecognition(e){if(await new Promise(e=>setTimeout(e,1e3+2e3*Math.random())),e.size/1e3<1)return null;let s=["我有三年的前端开发经验，主要使用React和Vue.js框架。","我最大的优势是学习能力强，能够快速适应新技术和新环境。","我希望在贵公司能够承担更多的技术挑战，提升自己的技术水平。","我对这个职位很感兴趣，因为它符合我的职业规划和发展方向。","我在之前的项目中负责了整个前端架构的设计和实现。","我认为团队协作非常重要，我善于与不同背景的同事沟通合作。","我会持续关注行业动态，学习新的技术和最佳实践。","我希望能够在技术和管理方面都有所发展。"];return{text:s[Math.floor(Math.random()*s.length)],confidence:.8+.2*Math.random(),isFinal:!0,timestamp:new Date}}setOnDataCallback(e){this.onDataCallback=e}setOnSpeechCallback(e){this.onSpeechCallback=e}getRecordingState(){return this.isRecording}async cleanup(){this.isRecording&&this.stopRecording(),this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null),this.audioContext&&(await this.audioContext.close(),this.audioContext=null),this.mediaRecorder=null,this.analyser=null,this.microphone=null,this.dataArray=null}static isSupported(){return!!("undefined"!=typeof navigator&&navigator.mediaDevices&&"function"==typeof navigator.mediaDevices.getUserMedia&&window.MediaRecorder&&(window.AudioContext||window.webkitAudioContext))}static async requestPermissions(){try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(e=>e.stop()),!0}catch(e){return console.error("Microphone permission denied:",e),!1}}constructor(e={sampleRate:44100,channels:1,bitDepth:16,echoCancellation:!0,noiseSuppression:!0}){this.config=e,this.mediaRecorder=null,this.audioContext=null,this.analyser=null,this.microphone=null,this.dataArray=null,this.stream=null,this.isRecording=!1,this.audioChunks=[]}}class x{async analyzeResponse(e,s){this.conversationHistory.push({type:"user",content:e,timestamp:new Date}),await new Promise(e=>setTimeout(e,1e3+1500*Math.random()));let t=this.analyzeResponseQuality(e);return this.generateFeedback(e,t,s)}async analyzeQuestion(e){await new Promise(e=>setTimeout(e,500));let s=this.classifyQuestion(e);return{questionType:s,difficulty:this.assessQuestionDifficulty(e),keywords:this.extractKeywords(e),expectedAnswerStructure:this.getAnswerStructure(s),tips:this.getQuestionSpecificTips(s,e)}}async generateProactiveSuggestion(){let e=this.conversationHistory.slice(-3);if(e.length<2)return null;let s=this.analyzeResponsePatterns(e);return s.needsImprovement?{type:"suggestion",content:s.suggestion,confidence:.8,category:s.category,suggestions:s.actionItems}:null}analyzeResponseQuality(e){let s=e.split(" ").length;return{length:s<20?"too_short":s>200?"too_long":"appropriate",structure:this.assessStructure(e),specificity:this.assessSpecificity(e),confidence:this.assessConfidence(e)}}assessStructure(e){let s=/^(我认为|我觉得|在我看来|根据我的经验)/.test(e),t=+!!s+ +!!/(例如|比如|举个例子|具体来说)/.test(e)+ +!!/(总的来说|综上所述|因此|所以)/.test(e);return t>=2?"excellent":1===t?"good":"poor"}assessSpecificity(e){let s=[/\d+年/,/\d+个月/,/\d+%/,/具体来说/,/项目中/,/公司/,/技术栈/].reduce((s,t)=>s+ +!!t.test(e),0);return s>=3?"very_specific":s>=1?"specific":"vague"}assessConfidence(e){let s=[/我确信/,/我相信/,/我擅长/,/我能够/].some(s=>s.test(e)),t=[/我觉得可能/,/也许/,/我不太确定/,/可能/].some(s=>s.test(e));return s&&!t?"high":t&&!s?"low":"medium"}generateFeedback(e,s,t){let i={poor:"建议使用更清晰的结构来组织回答，比如：观点-例子-总结。",good:"回答结构不错，可以进一步优化逻辑顺序。",excellent:"回答结构非常清晰，逻辑性强！"},a="",r=[],n="suggestion";return"too_short"===s.length?(a="您的回答比较简短，建议提供更多细节和具体例子来支撑您的观点。",r.push("添加具体例子","提供更多细节","说明影响和结果")):"poor"===s.structure?(a=i.poor,r.push("使用STAR方法","先说观点再举例","最后总结要点")):"vague"===s.specificity?(a="建议提供更具体的例子、数据或项目经验来支撑您的回答。",r.push("提供具体数据","举实际项目例子","说明技术细节")):(a="回答很不错！"+i[s.structure],n="encouragement",r.push("保持这种回答风格","继续展现专业能力","适当补充相关经验")),"technical"===this.context.interviewType?r.push("展示技术深度","说明解决方案","提及最佳实践"):"behavioral"===this.context.interviewType&&r.push("使用STAR方法","强调团队合作","展示学习能力"),{type:n,content:a,suggestions:r.slice(0,4),confidence:.85,category:this.determineCategory(t||e)}}classifyQuestion(e){return["技术","代码","算法","架构","框架","数据库","性能","优化"].some(s=>e.includes(s))?"technical":["团队","冲突","挑战","失败","成功","领导","合作","沟通"].some(s=>e.includes(s))?"behavioral":["如果","假设","遇到","处理","解决","应对"].some(s=>e.includes(s))?"situational":"general"}assessQuestionDifficulty(e){return["架构","设计模式","性能优化","分布式","高并发"].some(s=>e.includes(s))?"hard":["介绍","了解","基础","简单"].some(s=>e.includes(s))?"easy":"medium"}extractKeywords(e){let s=["的","是","在","有","和","与","或","但","如果","那么","什么","怎么","为什么"];return e.split(/\s+|[，。！？；：]/).filter(e=>e.length>1&&!s.includes(e)).slice(0,5)}getAnswerStructure(e){return({technical:["理解问题","说明方案","举例说明","总结优势"],behavioral:["情况描述","任务说明","行动过程","结果总结"],situational:["分析情况","制定策略","执行步骤","预期结果"],general:["观点表达","支撑论据","具体例子","简要总结"]})[e]}getQuestionSpecificTips(e,s){let t={technical:["展示技术深度和广度","提供具体的代码示例或架构图","说明技术选择的原因","讨论性能和可维护性"],behavioral:["使用STAR方法组织回答","强调个人贡献和成长","展示团队合作能力","体现解决问题的思路"],situational:["展示分析问题的能力","提出多种解决方案","考虑风险和应对措施","体现决策思维过程"],general:["保持回答的逻辑性","提供具体的例子","展现积极的态度","与职位要求相关联"]};return t[e]||t.general}analyzeResponsePatterns(e){let s=e.map(e=>e.content).join(" ");return["我觉得","我认为","可能","应该"].reduce((e,t)=>e+(s.split(t).length-1),0)>5?{needsImprovement:!0,suggestion:"注意避免重复使用相同的表达方式，尝试使用更多样化的语言来表达观点。",category:"communication",actionItems:["使用同义词替换","变换句式结构","增加表达多样性"]}:e.reduce((e,s)=>e+ +("vague"!==this.assessSpecificity(s.content)),0)<e.length/2?{needsImprovement:!0,suggestion:"建议在回答中增加更多具体的例子、数据和项目经验，让回答更有说服力。",category:"general",actionItems:["提供具体数据","举实际例子","说明具体成果"]}:{needsImprovement:!1,suggestion:"",category:"general",actionItems:[]}}determineCategory(e){return/技术|代码|架构|算法/.test(e)?"technical":/团队|沟通|合作|领导/.test(e)?"communication":/经验|挑战|成长|学习/.test(e)?"behavioral":"general"}getConversationSummary(){let e=this.conversationHistory.filter(e=>"user"===e.type),s=e.length,t=e.reduce((e,s)=>e+s.content.split(" ").length,0)/s,i=e.map(e=>this.analyzeResponseQuality(e.content)),a=[],r=[],n=i.map(e=>e.structure),l=i.map(e=>e.specificity),c=i.map(e=>e.confidence);n.filter(e=>"excellent"===e).length>s/2&&a.push("回答结构清晰"),l.filter(e=>"very_specific"===e).length>s/2&&a.push("提供具体例子"),c.filter(e=>"high"===e).length>s/2&&a.push("表现自信"),n.filter(e=>"poor"===e).length>s/3&&r.push("改善回答结构"),l.filter(e=>"vague"===e).length>s/3&&r.push("增加具体细节");let o={poor:0,good:.7,excellent:1},d={vague:0,specific:.7,very_specific:1},u={low:0,medium:.6,high:1},h=Math.round(i.reduce((e,s)=>e+(o[s.structure]+d[s.specificity]+u[s.confidence])/3,0)/s*100);return{totalResponses:s,averageResponseLength:Math.round(t),strongPoints:a,improvementAreas:r,overallScore:h}}constructor(e){this.conversationHistory=[],this.context=e}}var p=t(9148),g=t(9376),y=t(4835),f=t(9803),v=t(646),b=t(5339),j=t(5690),w=t(4186),N=t(9676),A=t(8979),S=t(1007),R=t(5657),C=t(1497),D=t(2486),k=t(1951),T=t(9588),I=t(5273),M=t(463),z=t(688),F=t(6982),P=t(7292);function _(){let{user:e}=(0,l.A)(),s=(0,F.EM)(),[t,r]=(0,a.useState)(!1),[_,E]=(0,a.useState)(!1),[Q,q]=(0,a.useState)(0),[J,B]=(0,a.useState)(null),[O,$]=(0,a.useState)([]),[H,U]=(0,a.useState)(""),[Z,K]=(0,a.useState)(!1),[L,V]=(0,a.useState)(0),[W,G]=(0,a.useState)(!0),[X,Y]=(0,a.useState)(!1),[ee,es]=(0,a.useState)(null),[et,ei]=(0,a.useState)({company:"",position:"",interviewType:"technical",difficulty:"intermediate"}),ea=(0,a.useRef)(null),er=(0,a.useRef)(null),en=(0,a.useRef)(null),el=(0,a.useRef)(null),ec=(0,a.useRef)(new p.E);(0,a.useEffect)(()=>{(async()=>{let e=m.isSupported();G(e),e&&Y(await m.requestPermissions())})()},[]),(0,a.useEffect)(()=>{var e;null==(e=en.current)||e.scrollIntoView({behavior:"smooth"})},[O]),(0,a.useEffect)(()=>((null==J?void 0:J.status)==="active"?el.current=setInterval(()=>{V(e=>e+1)},1e3):el.current&&clearInterval(el.current),()=>{el.current&&clearInterval(el.current)}),[null==J?void 0:J.status]),(0,a.useEffect)(()=>()=>{ea.current&&ea.current.cleanup()},[]);let eo=e=>{let s=Math.floor(e/60);return"".concat(s.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))},ed=async()=>{if(!et.company||!et.position)return void s.warning("请填写完整信息","请填写公司名称和职位");if(!W)return void s.error("浏览器不支持","您的浏览器不支持音频录制功能");if(!X){if(!await m.requestPermissions())return void s.error("权限不足","需要麦克风权限才能使用语音功能");Y(!0),s.success("权限获取成功","麦克风权限已获取")}if(ea.current=new m,!await ea.current.initialize())return void s.error("初始化失败","音频初始化失败，请检查麦克风设置");ea.current.setOnDataCallback(e=>{es(e),q(e.volume)}),ea.current.setOnSpeechCallback(async e=>{if(e.isFinal&&e.text.trim()){let s={id:Date.now().toString(),type:"user",content:e.text,timestamp:e.timestamp};if($(e=>[...e,s]),er.current){K(!0);try{let s=await er.current.analyzeResponse(e.text),t={id:(Date.now()+1).toString(),type:"ai",content:s.content,timestamp:new Date,suggestions:s.suggestions};$(e=>[...e,t])}catch(e){console.error("AI analysis failed:",e)}finally{K(!1)}}}}),er.current=new x({company:et.company,position:et.position,interviewType:et.interviewType,difficulty:et.difficulty}),B({id:Date.now().toString(),company:et.company,position:et.position,interviewType:et.interviewType,difficulty:et.difficulty,startTime:new Date,status:"active"}),V(0),$([{id:Date.now().toString(),type:"ai",content:"欢迎来到".concat(et.company,"的").concat(et.position,"面试！我是您的AI面试助手，将为您提供实时建议和支持。请开始您的面试，我会在适当的时候为您提供帮助。"),timestamp:new Date}])},eu=async()=>{if(J&&e){B({...J,status:"completed"}),em();let s="面试结束。总时长：".concat(eo(L),"。感谢您使用JobPlus面试助手！"),t=null;if(er.current)try{let e=er.current.getConversationSummary();t={...e,keyInsights:e.strongPoints.length>0?e.strongPoints:["面试表现良好"],recommendedActions:e.improvementAreas.length>0?e.improvementAreas:["继续保持"]},s+="\n\n\uD83D\uDCCA 面试总结：\n• 回答次数：".concat(t.totalResponses,"\n• 平均回答长度：").concat(t.averageResponseLength,"字\n• 综合评分：").concat(t.overallScore,"/100"),t.strongPoints.length>0&&(s+="\n• 优势：".concat(t.strongPoints.join("、"))),t.improvementAreas.length>0&&(s+="\n• 改进建议：".concat(t.improvementAreas.join("、")))}catch(e){console.error("Failed to generate summary:",e)}let i=[...O,{id:Date.now().toString(),type:"system",content:s,timestamp:new Date}];$(i);try{let s={id:J.id,userId:e.id,company:J.company,position:J.position,interviewType:J.interviewType,difficulty:J.difficulty,startTime:J.startTime,endTime:new Date,duration:L,status:"completed",messages:i.map(e=>({id:e.id,type:e.type,content:e.content,timestamp:e.timestamp,isQuestion:e.isQuestion,suggestions:e.suggestions})),summary:t||void 0};ec.current.saveSession(s)?console.log("Interview session saved successfully"):console.error("Failed to save interview session")}catch(e){console.error("Error saving interview session:",e)}ea.current&&(await ea.current.cleanup(),ea.current=null)}},eh=async()=>{if(!ea.current)return void s.error("系统错误","音频处理器未初始化");ea.current.startRecording()?(r(!0),E(!0),s.success("录音开始","正在监听您的语音...")):s.error("录音失败","无法开始录音，请检查麦克风权限")},em=()=>{ea.current&&t&&(ea.current.stopRecording(),r(!1),E(!1),q(0),es(null))},ex=async()=>{if(!H.trim())return;let e={id:Date.now().toString(),type:"user",content:H,timestamp:new Date};if($(s=>[...s,e]),U(""),er.current){K(!0);try{let e=await er.current.analyzeResponse(H),s={id:(Date.now()+1).toString(),type:"ai",content:e.content,timestamp:new Date,suggestions:e.suggestions};$(e=>[...e,s])}catch(s){console.error("AI analysis failed:",s);let e={id:(Date.now()+1).toString(),type:"ai",content:"收到您的消息，我正在分析中。请继续您的面试。",timestamp:new Date};$(s=>[...s,e])}finally{K(!1)}}};return J?(0,i.jsx)(c.A,{children:(0,i.jsx)(z.Z_,{children:(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex flex-col",children:[(0,i.jsx)("header",{className:"bg-white border-b flex-shrink-0",children:(0,i.jsx)("div",{className:"container mx-auto px-4 py-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.A,{className:"h-6 w-6 text-blue-600"}),(0,i.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"JobPlus"})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[J.company," - ",J.position]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,i.jsx)(w.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:eo(L)})]}),(0,i.jsx)(n(),{href:"/history",children:(0,i.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"历史记录"]})}),(0,i.jsxs)(o.$,{onClick:eu,variant:"destructive",size:"sm",children:[(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"结束面试"]})]})]})})}),(0,i.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,i.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,i.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[O.map(e=>(0,i.jsx)("div",{className:"flex ".concat("user"===e.type?"justify-end":"justify-start"),children:(0,i.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-2 rounded-lg ".concat("user"===e.type?"bg-blue-600 text-white":"ai"===e.type?"bg-green-100 text-green-900":"bg-gray-100 text-gray-900"),children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:["user"===e.type?(0,i.jsx)(S.A,{className:"h-4 w-4"}):"ai"===e.type?(0,i.jsx)(R.A,{className:"h-4 w-4"}):(0,i.jsx)(C.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"text-xs opacity-75",children:e.timestamp.toLocaleTimeString()})]}),(0,i.jsx)("p",{className:"text-sm",children:e.content}),e.suggestions&&(0,i.jsx)("div",{className:"mt-2 space-y-1",children:e.suggestions.map((e,s)=>(0,i.jsxs)("div",{className:"text-xs bg-white bg-opacity-20 px-2 py-1 rounded",children:["\uD83D\uDCA1 ",e]},s))})]})},e.id)),Z&&(0,i.jsx)("div",{className:"flex justify-start",children:(0,i.jsx)("div",{className:"bg-gray-100 text-gray-900 px-4 py-2 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"AI正在分析..."})]})})}),(0,i.jsx)("div",{ref:en})]}),(0,i.jsx)("div",{className:"border-t bg-white p-4",children:(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(d.p,{value:H,onChange:e=>U(e.target.value),placeholder:"输入您的问题或需要帮助的内容...",onKeyPress:e=>"Enter"===e.key&&ex(),className:"flex-1"}),(0,i.jsx)(o.$,{onClick:ex,disabled:!H.trim(),children:(0,i.jsx)(D.A,{className:"h-4 w-4"})})]})})]}),(0,i.jsxs)("div",{className:"w-80 border-l bg-white p-4 space-y-4",children:[(0,i.jsx)("h3",{className:"font-medium text-gray-900",children:"语音控制"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(o.$,{onClick:t?em:eh,className:"w-full ".concat(t?"bg-red-600 hover:bg-red-700":"bg-blue-600 hover:bg-blue-700"),size:"lg",children:t?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(k.A,{className:"h-5 w-5 mr-2"}),"停止录音"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(T.A,{className:"h-5 w-5 mr-2"}),"开始录音"]})}),_&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-600 flex items-center justify-between",children:[(0,i.jsx)("span",{children:"音频级别"}),(null==ee?void 0:ee.speechDetected)&&(0,i.jsxs)("span",{className:"text-xs text-green-600 flex items-center",children:[(0,i.jsx)(I.A,{className:"h-3 w-3 mr-1"}),"检测到语音"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"h-2 rounded-full transition-all duration-100 ".concat((null==ee?void 0:ee.speechDetected)?"bg-green-600":"bg-blue-600"),style:{width:"".concat(Q,"%")}})}),ee&&(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["音量: ",Math.round(Q),"% | 频率: ",Math.round(ee.frequency),"Hz"]})]})]}),(0,i.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium text-yellow-900 mb-2 flex items-center",children:[(0,i.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"面试技巧"]}),(0,i.jsxs)("ul",{className:"text-xs text-yellow-800 space-y-1",children:[(0,i.jsx)("li",{children:"• 保持眼神交流"}),(0,i.jsx)("li",{children:"• 回答要具体有例子"}),(0,i.jsx)("li",{children:"• 展示学习能力"}),(0,i.jsx)("li",{children:"• 提问显示兴趣"})]})]}),(0,i.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"本次面试"}),(0,i.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,i.jsxs)("div",{children:["时长: ",eo(L)]}),(0,i.jsxs)("div",{children:["消息: ",O.length]}),(0,i.jsxs)("div",{children:["状态: ","active"===J.status?"进行中":"已暂停"]})]})]})]})]})]})})}):(0,i.jsx)(c.A,{children:(0,i.jsx)(z.Z_,{children:(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,i.jsx)(z._A,{direction:"down",children:(0,i.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g.A,{className:"h-8 w-8 text-blue-600"}),(0,i.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,i.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,i.jsx)(n(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"控制台"}),(0,i.jsx)(n(),{href:"/resume",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"简历中心"}),(0,i.jsx)(n(),{href:"/interview",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"面试室"}),(0,i.jsx)(n(),{href:"/history",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试记录"}),(0,i.jsx)(o.$,{variant:"ghost",size:"icon",onClick:()=>P.f.signOut(),className:"hover:bg-red-50 hover:text-red-600",children:(0,i.jsx)(y.A,{className:"h-4 w-4"})})]})]})})}),(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,i.jsx)(z._A,{delay:200,children:(0,i.jsxs)("div",{className:"mb-8 text-center",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:"AI面试助手"}),(0,i.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"设置您的面试信息，AI助手将为您提供实时建议和指导"})]})}),(0,i.jsx)(z._A,{delay:400,children:(0,i.jsxs)(h.Zp,{className:"shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,i.jsxs)(h.aR,{className:"text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:[(0,i.jsxs)(h.ZB,{className:"text-2xl text-gray-800 flex items-center justify-center space-x-2",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,i.jsx)(f.A,{className:"h-6 w-6 text-white"})}),(0,i.jsx)("span",{children:"开始面试"})]}),(0,i.jsx)(h.BT,{className:"text-gray-600",children:"设置您的面试信息，AI助手将为您提供实时建议"})]}),(0,i.jsxs)(h.Wu,{className:"space-y-6 p-8",children:[(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(u.J,{htmlFor:"company",className:"text-gray-700 font-medium",children:"公司名称"}),(0,i.jsx)(d.p,{id:"company",value:et.company,onChange:e=>ei(s=>({...s,company:e.target.value})),placeholder:"请输入公司名称",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(u.J,{htmlFor:"position",className:"text-gray-700 font-medium",children:"应聘职位"}),(0,i.jsx)(d.p,{id:"position",value:et.position,onChange:e=>ei(s=>({...s,position:e.target.value})),placeholder:"请输入应聘职位",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400"})]})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(u.J,{className:"text-gray-700 font-medium",children:"面试类型"}),(0,i.jsxs)("select",{className:"w-full p-3 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-20 transition-all",value:et.interviewType,onChange:e=>ei(s=>({...s,interviewType:e.target.value})),children:[(0,i.jsx)("option",{value:"technical",children:"技术面试"}),(0,i.jsx)("option",{value:"behavioral",children:"行为面试"}),(0,i.jsx)("option",{value:"mixed",children:"综合面试"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(u.J,{className:"text-gray-700 font-medium",children:"难度级别"}),(0,i.jsxs)("select",{className:"w-full p-3 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-20 transition-all",value:et.difficulty,onChange:e=>ei(s=>({...s,difficulty:e.target.value})),children:[(0,i.jsx)("option",{value:"beginner",children:"初级"}),(0,i.jsx)("option",{value:"intermediate",children:"中级"}),(0,i.jsx)("option",{value:"advanced",children:"高级"})]})]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200",children:[(0,i.jsxs)("h4",{className:"font-medium text-blue-900 mb-3 flex items-center space-x-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"AI助手功能"})]}),(0,i.jsxs)("ul",{className:"text-sm text-blue-800 space-y-2",children:[(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,i.jsx)("span",{children:"实时语音识别和转录"})]}),(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,i.jsx)("span",{children:"智能回答建议和优化"})]}),(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,i.jsx)("span",{children:"面试技巧和要点提醒"})]}),(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,i.jsx)("span",{children:"表现评估和改进建议"})]})]})]}),(0,i.jsx)("div",{className:"p-4 rounded-lg border ".concat(W&&X?"bg-green-50 border-green-200":"bg-yellow-50 border-yellow-200"),children:(0,i.jsx)("div",{className:"flex items-center space-x-3",children:W&&X?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:(0,i.jsx)(v.A,{className:"h-4 w-4 text-white"})}),(0,i.jsx)("span",{className:"text-sm font-medium text-green-800",children:"音频功能已就绪"})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center",children:(0,i.jsx)(b.A,{className:"h-4 w-4 text-white"})}),(0,i.jsx)("span",{className:"text-sm font-medium text-yellow-800",children:W?"需要麦克风权限":"浏览器不支持音频录制"})]})})}),(0,i.jsxs)(o.$,{onClick:ed,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg",size:"lg",children:[(0,i.jsx)(j.A,{className:"h-5 w-5 mr-2"}),"开始面试"]})]})]})})]})})]})})})}},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var i=t(5155),a=t(2115),r=t(9434);let n=a.forwardRef((e,s)=>{let{className:t,type:a,...n}=e;return(0,i.jsx)("input",{type:a,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var i=t(5155),a=t(2115),r=t(7073),n=t(2085),l=t(9434);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,i.jsx)(r.b,{ref:s,className:(0,l.cn)(c(),t),...a})});o.displayName=r.b.displayName},8818:(e,s,t)=>{Promise.resolve().then(t.bind(t,1880))}},e=>{e.O(0,[598,874,386,642,441,964,358],()=>e(e.s=8818)),_N_E=e.O()}]);