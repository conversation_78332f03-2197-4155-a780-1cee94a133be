// Interview session storage and management

export interface InterviewMessage {
  id: string;
  type: 'user' | 'ai' | 'system' | 'interviewer';
  content: string;
  timestamp: Date;
  isQuestion?: boolean;
  suggestions?: string[];
  confidence?: number;
  category?: 'technical' | 'communication' | 'behavioral' | 'general';
}

export interface InterviewSession {
  id: string;
  userId: string;
  company: string;
  position: string;
  interviewType: 'technical' | 'behavioral' | 'mixed';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  status: 'preparing' | 'active' | 'paused' | 'completed' | 'cancelled';
  messages: InterviewMessage[];
  summary?: InterviewSummary;
  userNotes?: string;
  tags?: string[];
  rating?: number; // 1-5 stars
}

export interface InterviewSummary {
  totalResponses: number;
  averageResponseLength: number;
  strongPoints: string[];
  improvementAreas: string[];
  overallScore: number;
  technicalScore?: number;
  communicationScore?: number;
  behavioralScore?: number;
  keyInsights: string[];
  recommendedActions: string[];
}

export interface InterviewStats {
  totalSessions: number;
  totalDuration: number;
  averageScore: number;
  completionRate: number;
  topCompanies: Array<{ company: string; count: number }>;
  topPositions: Array<{ position: string; count: number }>;
  monthlyProgress: Array<{ month: string; sessions: number; avgScore: number }>;
  skillProgress: Array<{ skill: string; score: number; trend: 'up' | 'down' | 'stable' }>;
}

export class InterviewStorageService {
  private readonly STORAGE_KEY = 'jobplus_interview_sessions';
  private readonly STATS_KEY = 'jobplus_interview_stats';

  // Save interview session
  saveSession(session: InterviewSession): boolean {
    try {
      const sessions = this.getAllSessions();
      const existingIndex = sessions.findIndex(s => s.id === session.id);
      
      if (existingIndex >= 0) {
        sessions[existingIndex] = session;
      } else {
        sessions.push(session);
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
      this.updateStats();
      return true;
    } catch (error) {
      console.error('Failed to save interview session:', error);
      return false;
    }
  }

  // Get all sessions for current user
  getAllSessions(userId?: string): InterviewSession[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];
      
      const sessions: InterviewSession[] = JSON.parse(stored);
      
      // Convert date strings back to Date objects
      const processedSessions = sessions.map(session => ({
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : undefined,
        messages: session.messages.map(msg => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      }));

      // Filter by user if specified
      if (userId) {
        return processedSessions.filter(session => session.userId === userId);
      }
      
      return processedSessions;
    } catch (error) {
      console.error('Failed to load interview sessions:', error);
      return [];
    }
  }

  // Get session by ID
  getSessionById(sessionId: string): InterviewSession | null {
    const sessions = this.getAllSessions();
    return sessions.find(session => session.id === sessionId) || null;
  }

  // Delete session
  deleteSession(sessionId: string): boolean {
    try {
      const sessions = this.getAllSessions();
      const filteredSessions = sessions.filter(session => session.id !== sessionId);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredSessions));
      this.updateStats();
      return true;
    } catch (error) {
      console.error('Failed to delete interview session:', error);
      return false;
    }
  }

  // Update session notes
  updateSessionNotes(sessionId: string, notes: string): boolean {
    try {
      const sessions = this.getAllSessions();
      const sessionIndex = sessions.findIndex(s => s.id === sessionId);
      
      if (sessionIndex >= 0) {
        sessions[sessionIndex].userNotes = notes;
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to update session notes:', error);
      return false;
    }
  }

  // Update session rating
  updateSessionRating(sessionId: string, rating: number): boolean {
    try {
      const sessions = this.getAllSessions();
      const sessionIndex = sessions.findIndex(s => s.id === sessionId);
      
      if (sessionIndex >= 0) {
        sessions[sessionIndex].rating = Math.max(1, Math.min(5, rating));
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
        this.updateStats();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to update session rating:', error);
      return false;
    }
  }

  // Add tags to session
  updateSessionTags(sessionId: string, tags: string[]): boolean {
    try {
      const sessions = this.getAllSessions();
      const sessionIndex = sessions.findIndex(s => s.id === sessionId);
      
      if (sessionIndex >= 0) {
        sessions[sessionIndex].tags = tags;
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to update session tags:', error);
      return false;
    }
  }

  // Get sessions with filters
  getFilteredSessions(filters: {
    userId?: string;
    company?: string;
    position?: string;
    interviewType?: string;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
    minRating?: number;
    tags?: string[];
  }): InterviewSession[] {
    let sessions = this.getAllSessions(filters.userId);

    if (filters.company) {
      sessions = sessions.filter(s => 
        s.company.toLowerCase().includes(filters.company!.toLowerCase())
      );
    }

    if (filters.position) {
      sessions = sessions.filter(s => 
        s.position.toLowerCase().includes(filters.position!.toLowerCase())
      );
    }

    if (filters.interviewType) {
      sessions = sessions.filter(s => s.interviewType === filters.interviewType);
    }

    if (filters.status) {
      sessions = sessions.filter(s => s.status === filters.status);
    }

    if (filters.dateFrom) {
      sessions = sessions.filter(s => s.startTime >= filters.dateFrom!);
    }

    if (filters.dateTo) {
      sessions = sessions.filter(s => s.startTime <= filters.dateTo!);
    }

    if (filters.minRating) {
      sessions = sessions.filter(s => (s.rating || 0) >= filters.minRating!);
    }

    if (filters.tags && filters.tags.length > 0) {
      sessions = sessions.filter(s => 
        s.tags && s.tags.some(tag => filters.tags!.includes(tag))
      );
    }

    return sessions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  // Get interview statistics
  getStats(userId?: string): InterviewStats {
    try {
      const sessions = this.getAllSessions(userId);
      const completedSessions = sessions.filter(s => s.status === 'completed');

      if (completedSessions.length === 0) {
        return {
          totalSessions: 0,
          totalDuration: 0,
          averageScore: 0,
          completionRate: 0,
          topCompanies: [],
          topPositions: [],
          monthlyProgress: [],
          skillProgress: []
        };
      }

      const totalDuration = completedSessions.reduce((sum, s) => sum + s.duration, 0);
      const averageScore = completedSessions
        .filter(s => s.summary?.overallScore)
        .reduce((sum, s) => sum + (s.summary?.overallScore || 0), 0) / 
        completedSessions.filter(s => s.summary?.overallScore).length || 0;

      // Top companies
      const companyCount = new Map<string, number>();
      completedSessions.forEach(s => {
        companyCount.set(s.company, (companyCount.get(s.company) || 0) + 1);
      });
      const topCompanies = Array.from(companyCount.entries())
        .map(([company, count]) => ({ company, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Top positions
      const positionCount = new Map<string, number>();
      completedSessions.forEach(s => {
        positionCount.set(s.position, (positionCount.get(s.position) || 0) + 1);
      });
      const topPositions = Array.from(positionCount.entries())
        .map(([position, count]) => ({ position, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Monthly progress (last 6 months)
      const monthlyProgress = this.calculateMonthlyProgress(completedSessions);

      // Skill progress
      const skillProgress = this.calculateSkillProgress(completedSessions);

      return {
        totalSessions: sessions.length,
        totalDuration,
        averageScore: Math.round(averageScore),
        completionRate: Math.round((completedSessions.length / sessions.length) * 100),
        topCompanies,
        topPositions,
        monthlyProgress,
        skillProgress
      };
    } catch (error) {
      console.error('Failed to calculate stats:', error);
      return {
        totalSessions: 0,
        totalDuration: 0,
        averageScore: 0,
        completionRate: 0,
        topCompanies: [],
        topPositions: [],
        monthlyProgress: [],
        skillProgress: []
      };
    }
  }

  private calculateMonthlyProgress(sessions: InterviewSession[]): Array<{ month: string; sessions: number; avgScore: number }> {
    const monthlyData = new Map<string, { sessions: number; totalScore: number; count: number }>();
    
    sessions.forEach(session => {
      const monthKey = session.startTime.toISOString().substring(0, 7); // YYYY-MM
      const existing = monthlyData.get(monthKey) || { sessions: 0, totalScore: 0, count: 0 };
      
      existing.sessions++;
      if (session.summary?.overallScore) {
        existing.totalScore += session.summary.overallScore;
        existing.count++;
      }
      
      monthlyData.set(monthKey, existing);
    });

    return Array.from(monthlyData.entries())
      .map(([month, data]) => ({
        month,
        sessions: data.sessions,
        avgScore: data.count > 0 ? Math.round(data.totalScore / data.count) : 0
      }))
      .sort((a, b) => a.month.localeCompare(b.month))
      .slice(-6); // Last 6 months
  }

  private calculateSkillProgress(sessions: InterviewSession[]): Array<{ skill: string; score: number; trend: 'up' | 'down' | 'stable' }> {
    // Simplified skill progress calculation
    const skills = ['技术能力', '沟通表达', '逻辑思维', '团队协作', '学习能力'];
    
    return skills.map(skill => {
      const recentSessions = sessions.slice(-5); // Last 5 sessions
      const scores = recentSessions
        .filter(s => s.summary?.overallScore)
        .map(s => s.summary!.overallScore);
      
      const avgScore = scores.length > 0 ? 
        Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;
      
      // Simple trend calculation
      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (scores.length >= 3) {
        const firstHalf = scores.slice(0, Math.floor(scores.length / 2));
        const secondHalf = scores.slice(Math.floor(scores.length / 2));
        const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
        
        if (secondAvg > firstAvg + 5) trend = 'up';
        else if (secondAvg < firstAvg - 5) trend = 'down';
      }
      
      return { skill, score: avgScore, trend };
    });
  }

  private updateStats(): void {
    // This could be used to cache computed stats if needed
    // For now, we calculate stats on demand
  }

  // Export sessions to JSON
  exportSessions(userId?: string): string {
    const sessions = this.getAllSessions(userId);
    return JSON.stringify(sessions, null, 2);
  }

  // Import sessions from JSON
  importSessions(jsonData: string): boolean {
    try {
      const importedSessions: InterviewSession[] = JSON.parse(jsonData);
      const existingSessions = this.getAllSessions();
      
      // Merge sessions, avoiding duplicates
      const sessionMap = new Map<string, InterviewSession>();
      
      existingSessions.forEach(session => {
        sessionMap.set(session.id, session);
      });
      
      importedSessions.forEach(session => {
        sessionMap.set(session.id, session);
      });
      
      const mergedSessions = Array.from(sessionMap.values());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mergedSessions));
      this.updateStats();
      
      return true;
    } catch (error) {
      console.error('Failed to import sessions:', error);
      return false;
    }
  }

  // Clear all data
  clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.STATS_KEY);
      return true;
    } catch (error) {
      console.error('Failed to clear data:', error);
      return false;
    }
  }
}
