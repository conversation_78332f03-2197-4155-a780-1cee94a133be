directories:
  output: dist
  buildResources: build
appId: com.jobplus.app
productName: JobPlus AI面试助手
files:
  - filter:
      - .next/**/*
      - public/**/*
      - electron/**/*
      - node_modules/**/*
      - package.json
extraResources:
  - from: public
    to: public
    filter:
      - '**/*'
mac:
  category: public.app-category.productivity
  target:
    - target: dmg
      arch:
        - x64
        - arm64
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: public/icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
linux:
  target:
    - target: AppImage
      arch:
        - x64
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  installerIcon: public/icon.ico
  uninstallerIcon: public/icon.ico
  installerHeaderIcon: public/icon.ico
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: JobPlus AI面试助手
  include: build/installer.nsh
electronVersion: 37.2.5
