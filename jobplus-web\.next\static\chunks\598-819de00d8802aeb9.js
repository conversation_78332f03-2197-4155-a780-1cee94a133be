"use strict";(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[598],{2596:(e,r,o)=>{o.d(r,{$:()=>t});function t(){for(var e,r,o=0,t="",a=arguments.length;o<a;o++)(e=arguments[o])&&(r=function e(r){var o,t,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var n=r.length;for(o=0;o<n;o++)r[o]&&(t=e(r[o]))&&(a&&(a+=" "),a+=t)}else for(t in r)r[t]&&(a&&(a+=" "),a+=t);return a}(e))&&(t&&(t+=" "),t+=r);return t}},9688:(e,r,o)=>{o.d(r,{QP:()=>ee});let t=(e,r)=>{if(0===e.length)return r.classGroupId;let o=e[0],a=r.nextPart.get(o),n=a?t(e.slice(1),a):void 0;if(n)return n;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},a=/^\[(.+)\]$/,n=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:s(r,e)).classGroupId=o;return}if("function"==typeof e)return l(e)?void n(e(t),r,o,t):void r.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach(([e,a])=>{n(a,s(r,e),o,t)})})},s=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},l=e=>e.isThemeGetter,i=/\s+/;function d(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=c(e))&&(t&&(t+=" "),t+=r);return t}let c=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=c(e[t]))&&(o&&(o+=" "),o+=r);return o},m=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},p=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,u=/^\((?:(\w[\w-]*):)?(.+)\)$/i,b=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,h=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,k=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,w=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>b.test(e),y=e=>!!e&&!Number.isNaN(Number(e)),v=e=>!!e&&Number.isInteger(Number(e)),z=e=>e.endsWith("%")&&y(e.slice(0,-1)),j=e=>f.test(e),C=()=>!0,N=e=>g.test(e)&&!h.test(e),M=()=>!1,E=e=>k.test(e),P=e=>w.test(e),$=e=>!A(e)&&!O(e),G=e=>Q(e,J,M),A=e=>p.test(e),I=e=>Q(e,K,N),W=e=>Q(e,V,y),_=e=>Q(e,F,M),S=e=>Q(e,H,P),L=e=>Q(e,Y,E),O=e=>u.test(e),T=e=>D(e,K),q=e=>D(e,X),R=e=>D(e,F),U=e=>D(e,J),Z=e=>D(e,H),B=e=>D(e,Y,!0),Q=(e,r,o)=>{let t=p.exec(e);return!!t&&(t[1]?r(t[1]):o(t[2]))},D=(e,r,o=!1)=>{let t=u.exec(e);return!!t&&(t[1]?r(t[1]):o)},F=e=>"position"===e||"percentage"===e,H=e=>"image"===e||"url"===e,J=e=>"length"===e||"size"===e||"bg-size"===e,K=e=>"length"===e,V=e=>"number"===e,X=e=>"family-name"===e,Y=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...r){let o,s,l,c=function(i){let d;return s=(o={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,a=(a,n)=>{o.set(a,n),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(a(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):a(e,r)}}})((d=r.reduce((e,r)=>r(e),e())).cacheSize),parseClassName:(e=>{let{prefix:r,experimentalParseClassName:o}=e,t=e=>{let r,o,t=[],a=0,n=0,s=0;for(let o=0;o<e.length;o++){let l=e[o];if(0===a&&0===n){if(":"===l){t.push(e.slice(s,o)),s=o+1;continue}if("/"===l){r=o;continue}}"["===l?a++:"]"===l?a--:"("===l?n++:")"===l&&n--}let l=0===t.length?e:e.substring(s),i=(o=l).endsWith("!")?o.substring(0,o.length-1):o.startsWith("!")?o.substring(1):o;return{modifiers:t,hasImportantModifier:i!==l,baseClassName:i,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};if(r){let e=r+":",o=t;t=r=>r.startsWith(e)?o(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(o){let e=t;t=r=>o({className:r,parseClassName:e})}return t})(d),sortModifiers:(e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],t=[];return e.forEach(e=>{"["===e[0]||r[e]?(o.push(...t.sort(),e),t=[]):t.push(e)}),o.push(...t.sort()),o}})(d),...(e=>{let r=(e=>{let{theme:r,classGroups:o}=e,t={nextPart:new Map,validators:[]};for(let e in o)n(o[e],t,e,r);return t})(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),t(o,r)||(e=>{if(a.test(e)){let r=a.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}})(e)},getConflictingClassGroupIds:(e,r)=>{let t=o[e]||[];return r&&s[e]?[...t,...s[e]]:t}}})(d)}).cache.get,l=o.cache.set,c=m,m(i)};function m(e){let r=s(e);if(r)return r;let t=((e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:a,sortModifiers:n}=r,s=[],l=e.trim().split(i),d="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{isExternal:i,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=o(r);if(i){d=r+(d.length>0?" "+d:d);continue}let b=!!u,f=t(b?p.substring(0,u):p);if(!f){if(!b||!(f=t(p))){d=r+(d.length>0?" "+d:d);continue}b=!1}let g=n(c).join(":"),h=m?g+"!":g,k=h+f;if(s.includes(k))continue;s.push(k);let w=a(f,b);for(let e=0;e<w.length;++e){let r=w[e];s.push(h+r)}d=r+(d.length>0?" "+d:d)}return d})(e,o);return l(e,t),t}return function(){return c(d.apply(null,arguments))}}(()=>{let e=m("color"),r=m("font"),o=m("text"),t=m("font-weight"),a=m("tracking"),n=m("leading"),s=m("breakpoint"),l=m("container"),i=m("spacing"),d=m("radius"),c=m("shadow"),p=m("inset-shadow"),u=m("text-shadow"),b=m("drop-shadow"),f=m("blur"),g=m("perspective"),h=m("aspect"),k=m("ease"),w=m("animate"),N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...M(),O,A],P=()=>["auto","hidden","clip","visible","scroll"],Q=()=>["auto","contain","none"],D=()=>[O,A,i],F=()=>[x,"full","auto",...D()],H=()=>[v,"none","subgrid",O,A],J=()=>["auto",{span:["full",v,O,A]},v,O,A],K=()=>[v,"auto",O,A],V=()=>["auto","min","max","fr",O,A],X=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Y=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...D()],er=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...D()],eo=()=>[e,O,A],et=()=>[...M(),R,_,{position:[O,A]}],ea=()=>["no-repeat",{repeat:["","x","y","space","round"]}],en=()=>["auto","cover","contain",U,G,{size:[O,A]}],es=()=>[z,T,I],el=()=>["","none","full",d,O,A],ei=()=>["",y,T,I],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[y,z,R,_],ep=()=>["","none",f,O,A],eu=()=>["none",y,O,A],eb=()=>["none",y,O,A],ef=()=>[y,O,A],eg=()=>[x,"full",...D()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[C],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[$],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",y],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,A,O,h]}],container:["container"],columns:[{columns:[y,A,O,l]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:Q()}],"overscroll-x":[{"overscroll-x":Q()}],"overscroll-y":[{"overscroll-y":Q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:F()}],"inset-x":[{"inset-x":F()}],"inset-y":[{"inset-y":F()}],start:[{start:F()}],end:[{end:F()}],top:[{top:F()}],right:[{right:F()}],bottom:[{bottom:F()}],left:[{left:F()}],visibility:["visible","invisible","collapse"],z:[{z:[v,"auto",O,A]}],basis:[{basis:[x,"full","auto",l,...D()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[y,x,"auto","initial","none",A]}],grow:[{grow:["",y,O,A]}],shrink:[{shrink:["",y,O,A]}],order:[{order:[v,"first","last","none",O,A]}],"grid-cols":[{"grid-cols":H()}],"col-start-end":[{col:J()}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":H()}],"row-start-end":[{row:J()}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":V()}],"auto-rows":[{"auto-rows":V()}],gap:[{gap:D()}],"gap-x":[{"gap-x":D()}],"gap-y":[{"gap-y":D()}],"justify-content":[{justify:[...X(),"normal"]}],"justify-items":[{"justify-items":[...Y(),"normal"]}],"justify-self":[{"justify-self":["auto",...Y()]}],"align-content":[{content:["normal",...X()]}],"align-items":[{items:[...Y(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Y(),{baseline:["","last"]}]}],"place-content":[{"place-content":X()}],"place-items":[{"place-items":[...Y(),"baseline"]}],"place-self":[{"place-self":["auto",...Y()]}],p:[{p:D()}],px:[{px:D()}],py:[{py:D()}],ps:[{ps:D()}],pe:[{pe:D()}],pt:[{pt:D()}],pr:[{pr:D()}],pb:[{pb:D()}],pl:[{pl:D()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":D()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":D()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[l,"screen",...er()]}],"min-w":[{"min-w":[l,"screen","none",...er()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[s]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",o,T,I]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,O,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",z,A]}],"font-family":[{font:[q,A,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,O,A]}],"line-clamp":[{"line-clamp":[y,"none",O,W]}],leading:[{leading:[n,...D()]}],"list-image":[{"list-image":["none",O,A]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",O,A]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:eo()}],"text-color":[{text:eo()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[y,"from-font","auto",O,I]}],"text-decoration-color":[{decoration:eo()}],"underline-offset":[{"underline-offset":[y,"auto",O,A]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O,A]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O,A]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:et()}],"bg-repeat":[{bg:ea()}],"bg-size":[{bg:en()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},v,O,A],radial:["",O,A],conic:[v,O,A]},Z,S]}],"bg-color":[{bg:eo()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:eo()}],"gradient-via":[{via:eo()}],"gradient-to":[{to:eo()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:eo()}],"border-color-x":[{"border-x":eo()}],"border-color-y":[{"border-y":eo()}],"border-color-s":[{"border-s":eo()}],"border-color-e":[{"border-e":eo()}],"border-color-t":[{"border-t":eo()}],"border-color-r":[{"border-r":eo()}],"border-color-b":[{"border-b":eo()}],"border-color-l":[{"border-l":eo()}],"divide-color":[{divide:eo()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[y,O,A]}],"outline-w":[{outline:["",y,T,I]}],"outline-color":[{outline:eo()}],shadow:[{shadow:["","none",c,B,L]}],"shadow-color":[{shadow:eo()}],"inset-shadow":[{"inset-shadow":["none",p,B,L]}],"inset-shadow-color":[{"inset-shadow":eo()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:eo()}],"ring-offset-w":[{"ring-offset":[y,I]}],"ring-offset-color":[{"ring-offset":eo()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":eo()}],"text-shadow":[{"text-shadow":["none",u,B,L]}],"text-shadow-color":[{"text-shadow":eo()}],opacity:[{opacity:[y,O,A]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[y]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":eo()}],"mask-image-linear-to-color":[{"mask-linear-to":eo()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":eo()}],"mask-image-t-to-color":[{"mask-t-to":eo()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":eo()}],"mask-image-r-to-color":[{"mask-r-to":eo()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":eo()}],"mask-image-b-to-color":[{"mask-b-to":eo()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":eo()}],"mask-image-l-to-color":[{"mask-l-to":eo()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":eo()}],"mask-image-x-to-color":[{"mask-x-to":eo()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":eo()}],"mask-image-y-to-color":[{"mask-y-to":eo()}],"mask-image-radial":[{"mask-radial":[O,A]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":eo()}],"mask-image-radial-to-color":[{"mask-radial-to":eo()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[y]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":eo()}],"mask-image-conic-to-color":[{"mask-conic-to":eo()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:et()}],"mask-repeat":[{mask:ea()}],"mask-size":[{mask:en()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",O,A]}],filter:[{filter:["","none",O,A]}],blur:[{blur:ep()}],brightness:[{brightness:[y,O,A]}],contrast:[{contrast:[y,O,A]}],"drop-shadow":[{"drop-shadow":["","none",b,B,L]}],"drop-shadow-color":[{"drop-shadow":eo()}],grayscale:[{grayscale:["",y,O,A]}],"hue-rotate":[{"hue-rotate":[y,O,A]}],invert:[{invert:["",y,O,A]}],saturate:[{saturate:[y,O,A]}],sepia:[{sepia:["",y,O,A]}],"backdrop-filter":[{"backdrop-filter":["","none",O,A]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[y,O,A]}],"backdrop-contrast":[{"backdrop-contrast":[y,O,A]}],"backdrop-grayscale":[{"backdrop-grayscale":["",y,O,A]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[y,O,A]}],"backdrop-invert":[{"backdrop-invert":["",y,O,A]}],"backdrop-opacity":[{"backdrop-opacity":[y,O,A]}],"backdrop-saturate":[{"backdrop-saturate":[y,O,A]}],"backdrop-sepia":[{"backdrop-sepia":["",y,O,A]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":D()}],"border-spacing-x":[{"border-spacing-x":D()}],"border-spacing-y":[{"border-spacing-y":D()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",O,A]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[y,"initial",O,A]}],ease:[{ease:["linear","initial",k,O,A]}],delay:[{delay:[y,O,A]}],animate:[{animate:["none",w,O,A]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,O,A]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:eb()}],"scale-x":[{"scale-x":eb()}],"scale-y":[{"scale-y":eb()}],"scale-z":[{"scale-z":eb()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[O,A,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:eo()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:eo()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O,A]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O,A]}],fill:[{fill:["none",...eo()]}],"stroke-w":[{stroke:[y,T,I,W]}],stroke:[{stroke:["none",...eo()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9946:(e,r,o)=>{o.d(r,{A:()=>i});var t=o(2115);let a=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,o)=>o?o.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},n=function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return r.filter((e,r,o)=>!!e&&""!==e.trim()&&o.indexOf(e)===r).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,t.forwardRef)((e,r)=>{let{color:o="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:d="",children:c,iconNode:m,...p}=e;return(0,t.createElement)("svg",{ref:r,...s,width:a,height:a,stroke:o,strokeWidth:i?24*Number(l)/Number(a):l,className:n("lucide",d),...!c&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(p)&&{"aria-hidden":"true"},...p},[...m.map(e=>{let[r,o]=e;return(0,t.createElement)(r,o)}),...Array.isArray(c)?c:[c]])}),i=(e,r)=>{let o=(0,t.forwardRef)((o,s)=>{let{className:i,...d}=o;return(0,t.createElement)(l,{ref:s,iconNode:r,className:n("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...d})});return o.displayName=a(e),o}}}]);