(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[105],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>o});var s=r(5155),a=r(2115),n=r(7292);let l=(0,a.createContext)({user:null,loading:!0,signOut:async()=>{}}),i=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[o,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{var e;let{data:{session:t}}=await n.j.auth.getSession();i(null!=(e=null==t?void 0:t.user)?e:null),d(!1)})();let{data:{subscription:e}}=n.j.auth.onAuthStateChange(async(e,t)=>{var r;i(null!=(r=null==t?void 0:t.user)?r:null),d(!1)});return()=>e.unsubscribe()},[]);let c=async()=>{await n.j.auth.signOut()};return(0,s.jsx)(l.Provider,{value:{user:r,loading:o,signOut:c},children:t})}},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155),a=r(2115),n=r(4624),l=r(2085),i=r(9434);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:a,size:l,className:r})),ref:t,...c})});d.displayName="Button"},688:(e,t,r)=>{"use strict";r.d(t,{Oq:()=>o,Z_:()=>l,_A:()=>i});var s=r(5155),a=r(2115),n=r(9434);function l(e){let{children:t,className:r,delay:l=0}=e,[i,o]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{o(!0)},l);return()=>clearTimeout(e)},[l]),(0,s.jsx)("div",{className:(0,n.cn)("transition-all duration-500 ease-out",i?"opacity-100 translate-y-0":"opacity-0 translate-y-4",r),children:t})}function i(e){let{children:t,direction:r="up",delay:l=0,duration:i=500,className:o}=e,[d,c]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{c(!0)},l);return()=>clearTimeout(e)},[l]),(0,s.jsx)("div",{className:(0,n.cn)("transition-all ease-out",d?"opacity-100 translate-x-0 translate-y-0":"opacity-0 ".concat({up:"translate-y-4",down:"-translate-y-4",left:"translate-x-4",right:"-translate-x-4"}[r]),o),style:{transitionDuration:"".concat(i,"ms")},children:t})}function o(e){let{children:t,delay:r=0,staggerDelay:n=100,className:l}=e,o=a.Children.toArray(t);return(0,s.jsx)("div",{className:l,children:o.map((e,t)=>(0,s.jsx)(i,{delay:r+t*n,children:e},t))})}},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var s=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,l=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,o=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let n=a(t)||a(s);return l[e][n]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,o,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...d}[t]):({...i,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2699:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(5155),a=r(2115),n=r(6874),l=r.n(n),i=r(283),o=r(9053),d=r(285),c=r(6695),u=r(9376),h=r(4835),m=r(9803),x=r(4186),f=r(3109),p=r(1007),g=r(7434),y=r(4616),b=r(688),j=r(9946);let v=(0,j.A)("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]),N=(0,j.A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);function w(){let[e,t]=(0,a.useState)(!1),r=function(){let{isElectron:e}=function(){let[e,t]=(0,a.useState)({isElectron:!1});return(0,a.useEffect)(()=>{window.isElectron&&window.electronAPI?window.electronAPI.getVersion().then(e=>{t({isElectron:!0,platform:window.electronAPI.platform,isDev:window.electronAPI.isDev,version:e})}).catch(()=>{t({isElectron:!0,platform:window.electronAPI.platform,isDev:window.electronAPI.isDev})}):t({isElectron:!1})},[]),e}();return e&&window.electronAPI?window.electronAPI:null}();(0,a.useEffect)(()=>{r&&r.isFullscreen().then(t)},[r]);let n=async()=>{if(r)try{let e=await r.toggleFullscreen();t(e)}catch(e){console.error("Failed to toggle fullscreen:",e)}};return r?(0,s.jsx)(d.$,{variant:"ghost",size:"icon",onClick:n,className:"hover:bg-gray-100 dark:hover:bg-gray-800",title:e?"退出全屏":"进入全屏",children:e?(0,s.jsx)(v,{className:"h-4 w-4"}):(0,s.jsx)(N,{className:"h-4 w-4"})}):null}function A(){var e;let{user:t,signOut:r}=(0,i.A)(),[n,j]=(0,a.useState)({totalSessions:0,totalDuration:0,remainingCredits:100,planType:"free"}),[v,N]=(0,a.useState)([{id:"1",company:"阿里巴巴",position:"前端工程师",date:"2024-08-01",duration:1800,status:"completed"},{id:"2",company:"腾讯",position:"产品经理",date:"2024-07-28",duration:2400,status:"completed"}]),A=e=>{let t=Math.floor(e/60);return"".concat(t,"分钟")};return(0,s.jsx)(o.A,{children:(0,s.jsx)(b.Z_,{children:(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,s.jsx)(b._A,{direction:"down",children:(0,s.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,s.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,s.jsx)(l(),{href:"/dashboard",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"控制台"}),(0,s.jsx)(l(),{href:"/resume",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"简历中心"}),(0,s.jsx)(l(),{href:"/interview",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试室"}),(0,s.jsx)(l(),{href:"/history",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试记录"}),(0,s.jsx)(w,{}),(0,s.jsx)(d.$,{variant:"ghost",size:"icon",onClick:r,className:"hover:bg-red-50 hover:text-red-600",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]})]})})}),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(b._A,{delay:200,children:(0,s.jsxs)("div",{className:"mb-8 text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:["欢迎回来，",(null==t||null==(e=t.user_metadata)?void 0:e.username)||(null==t?void 0:t.email),"！"]}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"准备好开始您的下一次面试了吗？让AI助手帮您在面试中脱颖而出。"})]})}),(0,s.jsx)(b._A,{delay:400,children:(0,s.jsxs)("div",{className:"grid md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsxs)(c.Zp,{className:"hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-white",children:[(0,s.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(c.ZB,{className:"text-sm font-medium text-blue-700",children:"总面试次数"}),(0,s.jsx)(m.A,{className:"h-5 w-5 text-blue-500"})]}),(0,s.jsxs)(c.Wu,{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:n.totalSessions}),(0,s.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"+2 较上月"})]})]}),(0,s.jsxs)(c.Zp,{className:"hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500 bg-gradient-to-r from-green-50 to-white",children:[(0,s.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(c.ZB,{className:"text-sm font-medium text-green-700",children:"总使用时长"}),(0,s.jsx)(x.A,{className:"h-5 w-5 text-green-500"})]}),(0,s.jsxs)(c.Wu,{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-green-600",children:A(n.totalDuration)}),(0,s.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"+1小时 较上月"})]})]}),(0,s.jsxs)(c.Zp,{className:"hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500 bg-gradient-to-r from-purple-50 to-white",children:[(0,s.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(c.ZB,{className:"text-sm font-medium text-purple-700",children:"剩余积分"}),(0,s.jsx)(f.A,{className:"h-5 w-5 text-purple-500"})]}),(0,s.jsxs)(c.Wu,{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-purple-600",children:n.remainingCredits}),(0,s.jsx)("p",{className:"text-xs text-purple-600 font-medium",children:"free"===n.planType?"免费版":"高级版"})]})]}),(0,s.jsxs)(c.Zp,{className:"hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-white",children:[(0,s.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(c.ZB,{className:"text-sm font-medium text-orange-700",children:"账户状态"}),(0,s.jsx)(p.A,{className:"h-5 w-5 text-orange-500"})]}),(0,s.jsxs)(c.Wu,{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-orange-600",children:"活跃"}),(0,s.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:"邮箱已验证"})]})]})]})}),(0,s.jsx)(b._A,{delay:600,children:(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"快速操作"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,s.jsx)(c.Zp,{className:"group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:(0,s.jsx)(l(),{href:"/interview",children:(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(c.ZB,{className:"text-xl text-blue-700 group-hover:text-blue-800",children:"开始面试"}),(0,s.jsx)(c.BT,{className:"text-blue-600",children:"进入AI面试助手，开始您的面试准备"})]})})}),(0,s.jsx)(c.Zp,{className:"group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:(0,s.jsx)(l(),{href:"/resume",children:(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,s.jsx)(g.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(c.ZB,{className:"text-xl text-green-700 group-hover:text-green-800",children:"管理简历"}),(0,s.jsx)(c.BT,{className:"text-green-600",children:"上传或编辑您的简历信息"})]})})}),(0,s.jsx)(c.Zp,{className:"group cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200",children:(0,s.jsx)(l(),{href:"/history",children:(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(c.ZB,{className:"text-xl text-purple-700 group-hover:text-purple-800",children:"查看记录"}),(0,s.jsx)(c.BT,{className:"text-purple-600",children:"回顾您的面试历史和表现"})]})})})]})]})}),(0,s.jsx)(b._A,{delay:800,children:(0,s.jsxs)(c.Zp,{className:"shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,s.jsx)(c.aR,{className:"bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(c.ZB,{className:"text-xl text-gray-800",children:"最近面试记录"}),(0,s.jsx)(l(),{href:"/history",children:(0,s.jsx)(d.$,{variant:"outline",size:"sm",className:"hover:bg-blue-50 hover:border-blue-300",children:"查看全部"})})]})}),(0,s.jsx)(c.Wu,{className:"p-6",children:v.length>0?(0,s.jsx)("div",{className:"space-y-4",children:v.map((e,t)=>(0,s.jsx)(b._A,{delay:900+100*t,children:(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-all duration-200 hover:border-blue-200 bg-gradient-to-r from-white to-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,s.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-800",children:e.company}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.position})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-800",children:new Date(e.date).toLocaleDateString("zh-CN")}),(0,s.jsx)("p",{className:"text-sm text-blue-600 font-medium",children:A(e.duration)})]})]})},e.id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(m.A,{className:"h-10 w-10 text-blue-500"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"还没有面试记录"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"开始您的第一次AI面试练习吧！"}),(0,s.jsx)(l(),{href:"/interview",children:(0,s.jsxs)(d.$,{className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"开始第一次面试"]})})]})})]})})]})]})})})}},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4624:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,TL:()=>l});var s=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var n=r(5155);function l(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var l;let e,i,o=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...l}=e,i=s.Children.toArray(a),o=i.find(d);if(o){let e=o.props.children,a=i.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),o=Symbol("radix.slottable");function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},4835:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var s=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},7292:(e,t,r)=>{"use strict";r.d(t,{f:()=>a,j:()=>n});class s{async signUp(e,t,r){await new Promise(e=>setTimeout(e,1e3));let s={id:Date.now().toString(),email:e,user_metadata:r||{}};return this.currentUser=s,this.notifyListeners(),{data:{user:s,session:{access_token:"mock-token"}},error:null}}async signInWithPassword(e,t){if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e&&"demo123"===t){let t={id:"1",email:e,user_metadata:{username:"演示用户"}};return this.currentUser=t,this.notifyListeners(),{data:{user:t,session:{access_token:"mock-token"}},error:null}}return{data:{user:null,session:null},error:{message:"邮箱或密码错误"}}}async signOut(){return this.currentUser=null,this.notifyListeners(),{error:null}}async resetPasswordForEmail(e){return await new Promise(e=>setTimeout(e,1e3)),{error:null}}async updateUser(e){return await new Promise(e=>setTimeout(e,1e3)),this.currentUser&&(this.currentUser={...this.currentUser,...e},this.notifyListeners()),{error:null}}async getSession(){return{data:{session:this.currentUser?{user:this.currentUser}:null}}}onAuthStateChange(e){let t=t=>{e(t?"SIGNED_IN":"SIGNED_OUT",t?{user:t}:null)};return this.listeners.push(t),{data:{subscription:{unsubscribe:()=>{let e=this.listeners.indexOf(t);e>-1&&this.listeners.splice(e,1)}}}}}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}constructor(){this.currentUser=null,this.listeners=[]}}let a=new s,n={auth:a}},7434:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(5155),a=r(2115),n=r(5695),l=r(283);function i(e){let{children:t,redirectTo:r="/auth/login"}=e,{user:i,loading:o}=(0,l.A)(),d=(0,n.useRouter)();return((0,a.useEffect)(()=>{o||i||d.push(r)},[i,o,d,r]),o)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):i?(0,s.jsx)(s.Fragment,{children:t}):null}},9297:(e,t,r)=>{Promise.resolve().then(r.bind(r,2699))},9376:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]])},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},9803:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])}},e=>{e.O(0,[598,874,441,964,358],()=>e(e.s=9297)),_N_E=e.O()}]);