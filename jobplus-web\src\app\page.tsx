import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Brain, Shield, Zap, Users } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">JobPlus</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="#features" className="text-gray-600 hover:text-gray-900">
              功能特色
            </Link>
            <Link href="#how-it-works" className="text-gray-600 hover:text-gray-900">
              使用方法
            </Link>
            <Link href="/auth/login" className="text-gray-600 hover:text-gray-900">
              登录
            </Link>
            <Link href="/auth/register">
              <Button>免费注册</Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            AI面试助手
            <span className="text-blue-600 block">让面试更从容</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            JobPlus通过AI实时分析面试问题，为您提供专业的回答思路和要点提示，
            帮助您在面试中更加自信从容，展现最佳状态。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="text-lg px-8 py-3">
                立即开始免费体验
              </Button>
            </Link>
            <Link href="#how-it-works">
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                了解工作原理
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            为什么选择JobPlus？
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <Brain className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle>AI智能分析</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  实时识别面试官问题，AI智能分析并提供结构化回答思路
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Zap className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <CardTitle>实时响应</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  毫秒级响应速度，在面试过程中提供即时帮助
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Shield className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <CardTitle>隐私安全</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  端到端加密，保护您的面试内容和个人信息安全
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="h-12 w-12 text-orange-600 mx-auto mb-4" />
                <CardTitle>专业指导</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  基于简历信息提供个性化建议，让回答更贴合岗位要求
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section id="how-it-works" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            三步开始使用
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">上传简历</h3>
              <p className="text-gray-600">
                上传您的简历，AI将自动解析并提取关键信息
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">开启面试室</h3>
              <p className="text-gray-600">
                在面试时打开JobPlus面试室，开始音频捕获
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">获得AI建议</h3>
              <p className="text-gray-600">
                实时获得AI提供的回答要点和思路提示
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Brain className="h-8 w-8 text-blue-400" />
            <span className="text-2xl font-bold">JobPlus</span>
          </div>
          <p className="text-gray-400 mb-4">
            让每一次面试都成为成功的开始
          </p>
          <div className="flex justify-center space-x-6 text-sm text-gray-400">
            <Link href="/privacy" className="hover:text-white">隐私政策</Link>
            <Link href="/terms" className="hover:text-white">服务条款</Link>
            <Link href="/contact" className="hover:text-white">联系我们</Link>
          </div>
          <p className="text-gray-500 text-sm mt-4">
            © 2024 JobPlus. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
