"use client";

import { useState, useRef } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Brain, 
  Upload, 
  FileText, 
  User, 
  GraduationCap, 
  Briefcase, 
  Code,
  ArrowLeft,
  Save,
  Edit,
  Plus,
  Trash2,
  Loader2,
  LogOut
} from "lucide-react";
import { PageTransition, FadeIn } from "@/components/ui/page-transition";
import { useToastActions } from "@/components/ui/toast";
import { mockAuth } from "@/lib/mock-auth";

interface PersonalInfo {
  name: string;
  phone: string;
  email: string;
  city: string;
}

interface Education {
  id: string;
  school: string;
  degree: string;
  major: string;
  start_date: string;
  end_date: string;
}

interface WorkExperience {
  id: string;
  company: string;
  position: string;
  start_date: string;
  end_date: string;
  responsibilities: string[];
}

interface Project {
  id: string;
  name: string;
  role: string;
  description: string;
  technologies: string[];
}

interface Skills {
  programming_languages: string[];
  frameworks: string[];
  databases: string[];
  tools: string[];
}

export default function ResumePage() {
  const { user } = useAuth();
  const toast = useToastActions();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [hasResume, setHasResume] = useState(false);

  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    name: "",
    phone: "",
    email: user?.email || "",
    city: "",
  });

  const [education, setEducation] = useState<Education[]>([]);
  const [workExperience, setWorkExperience] = useState<WorkExperience[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [skills, setSkills] = useState<Skills>({
    programming_languages: [],
    frameworks: [],
    databases: [],
    tools: [],
  });

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setIsParsing(true);

    try {
      // Simulate file upload and AI parsing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock parsed data
      setPersonalInfo({
        name: "张三",
        phone: "13800138000",
        email: user?.email || "",
        city: "北京",
      });

      setEducation([{
        id: "1",
        school: "清华大学",
        degree: "本科",
        major: "计算机科学与技术",
        start_date: "2018-09",
        end_date: "2022-06",
      }]);

      setWorkExperience([{
        id: "1",
        company: "阿里巴巴",
        position: "前端工程师",
        start_date: "2022-07",
        end_date: "2024-08",
        responsibilities: [
          "负责电商平台前端开发",
          "使用React和TypeScript构建用户界面",
          "优化页面性能，提升用户体验"
        ],
      }]);

      setProjects([{
        id: "1",
        name: "电商管理系统",
        role: "前端负责人",
        description: "基于React的电商后台管理系统，支持商品管理、订单处理等功能",
        technologies: ["React", "TypeScript", "Ant Design", "Redux"],
      }]);

      setSkills({
        programming_languages: ["JavaScript", "TypeScript", "Python"],
        frameworks: ["React", "Vue.js", "Node.js"],
        databases: ["MySQL", "MongoDB", "Redis"],
        tools: ["Git", "Docker", "Webpack"],
      });

      setHasResume(true);
      setIsEditing(true);
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setIsUploading(false);
      setIsParsing(false);
    }
  };

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      school: "",
      degree: "",
      major: "",
      start_date: "",
      end_date: "",
    };
    setEducation([...education, newEducation]);
  };

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    setEducation(education.map(edu => 
      edu.id === id ? { ...edu, [field]: value } : edu
    ));
  };

  const removeEducation = (id: string) => {
    setEducation(education.filter(edu => edu.id !== id));
  };

  const addWorkExperience = () => {
    const newWork: WorkExperience = {
      id: Date.now().toString(),
      company: "",
      position: "",
      start_date: "",
      end_date: "",
      responsibilities: [""],
    };
    setWorkExperience([...workExperience, newWork]);
  };

  const updateWorkExperience = (id: string, field: keyof WorkExperience, value: any) => {
    setWorkExperience(workExperience.map(work => 
      work.id === id ? { ...work, [field]: value } : work
    ));
  };

  const removeWorkExperience = (id: string) => {
    setWorkExperience(workExperience.filter(work => work.id !== id));
  };

  const saveResume = async () => {
    try {
      // Here you would save to Supabase
      console.log("Saving resume...", {
        personalInfo,
        education,
        workExperience,
        projects,
        skills,
      });
      setIsEditing(false);
    } catch (error) {
      console.error("Save failed:", error);
    }
  };

  return (
    <ProtectedRoute>
      <PageTransition>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Header */}
        <FadeIn direction="down">
          <header className="bg-white/80 backdrop-blur-sm border-b shadow-sm">
            <div className="container mx-auto px-4 py-4 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Brain className="h-8 w-8 text-blue-600" />
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  JobPlus
                </span>
              </div>
              <nav className="flex items-center space-x-4">
                <Link href="/dashboard" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                  控制台
                </Link>
                <Link href="/resume" className="text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50">
                  简历中心
                </Link>
                <Link href="/interview" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                  面试室
                </Link>
                <Link href="/history" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors">
                  面试记录
                </Link>
                {hasResume && (
                  <Button
                    onClick={() => setIsEditing(!isEditing)}
                    variant={isEditing ? "default" : "outline"}
                    className="ml-2"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {isEditing ? "预览模式" : "编辑模式"}
                  </Button>
                )}
                <Button variant="ghost" size="icon" onClick={() => mockAuth.signOut()} className="hover:bg-red-50 hover:text-red-600">
                  <LogOut className="h-4 w-4" />
                </Button>
              </nav>
            </div>
          </header>
        </FadeIn>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <FadeIn delay={200}>
              <div className="mb-8 text-center">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">
                  简历管理中心
                </h1>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  上传并管理您的简历，AI将帮助您优化内容并提取关键信息
                </p>
              </div>
            </FadeIn>

            {!hasResume ? (
              // Upload Section
              <FadeIn delay={400}>
                <Card className="text-center shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg">
                    <CardTitle className="flex items-center justify-center space-x-2 text-xl text-gray-800">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                        <FileText className="h-6 w-6 text-white" />
                      </div>
                      <span>上传您的简历</span>
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      支持PDF、Word格式，AI将自动解析并提取关键信息
                    </CardDescription>
                  </CardHeader>
                <CardContent className="space-y-6 p-8">
                  {isParsing ? (
                    <div className="py-12">
                      <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6">
                        <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
                      </div>
                      <p className="text-lg font-medium text-gray-800 mb-2">AI正在解析您的简历...</p>
                      <p className="text-gray-600">请稍候，这可能需要几秒钟</p>
                    </div>
                  ) : (
                    <>
                      <div
                        className="border-2 border-dashed border-blue-200 rounded-lg p-12 hover:border-blue-400 transition-all duration-300 cursor-pointer bg-gradient-to-br from-blue-50/50 to-white hover:shadow-lg group"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                          <Upload className="h-8 w-8 text-white" />
                        </div>
                        <p className="text-lg font-medium text-gray-800 mb-2">
                          点击上传简历文件
                        </p>
                        <p className="text-gray-600">
                          或拖拽文件到此处
                        </p>
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <div className="text-sm text-gray-500 text-center bg-gray-50 rounded-lg p-3">
                        支持格式：PDF, DOC, DOCX（最大10MB）
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
              </FadeIn>
            ) : (
              // Resume Content
              <div className="space-y-6">
                {isEditing && (
                  <FadeIn delay={400}>
                    <div className="flex justify-end">
                      <Button onClick={saveResume} className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg">
                        <Save className="h-4 w-4 mr-2" />
                        保存更改
                      </Button>
                    </div>
                  </FadeIn>
                )}

                {/* Personal Information */}
                <FadeIn delay={500}>
                  <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg">
                      <CardTitle className="flex items-center space-x-2 text-xl text-gray-800">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                          <User className="h-5 w-5 text-white" />
                        </div>
                        <span>个人信息</span>
                      </CardTitle>
                    </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">姓名</Label>
                        <Input
                          id="name"
                          value={personalInfo.name}
                          onChange={(e) => setPersonalInfo(prev => ({ ...prev, name: e.target.value }))}
                          disabled={!isEditing}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">手机号码</Label>
                        <Input
                          id="phone"
                          value={personalInfo.phone}
                          onChange={(e) => setPersonalInfo(prev => ({ ...prev, phone: e.target.value }))}
                          disabled={!isEditing}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">邮箱地址</Label>
                        <Input
                          id="email"
                          value={personalInfo.email}
                          onChange={(e) => setPersonalInfo(prev => ({ ...prev, email: e.target.value }))}
                          disabled={!isEditing}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="city">所在城市</Label>
                        <Input
                          id="city"
                          value={personalInfo.city}
                          onChange={(e) => setPersonalInfo(prev => ({ ...prev, city: e.target.value }))}
                          disabled={!isEditing}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                </FadeIn>

                {/* Education */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <GraduationCap className="h-5 w-5" />
                        <span>教育经历</span>
                      </CardTitle>
                      {isEditing && (
                        <Button onClick={addEducation} size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          添加
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {education.map((edu) => (
                        <div key={edu.id} className="p-4 border rounded-lg">
                          <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>学校</Label>
                              <Input
                                value={edu.school}
                                onChange={(e) => updateEducation(edu.id, 'school', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>学历</Label>
                              <Input
                                value={edu.degree}
                                onChange={(e) => updateEducation(edu.id, 'degree', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>专业</Label>
                              <Input
                                value={edu.major}
                                onChange={(e) => updateEducation(edu.id, 'major', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>时间</Label>
                              <div className="flex space-x-2">
                                <Input
                                  type="month"
                                  value={edu.start_date}
                                  onChange={(e) => updateEducation(edu.id, 'start_date', e.target.value)}
                                  disabled={!isEditing}
                                />
                                <Input
                                  type="month"
                                  value={edu.end_date}
                                  onChange={(e) => updateEducation(edu.id, 'end_date', e.target.value)}
                                  disabled={!isEditing}
                                />
                              </div>
                            </div>
                          </div>
                          {isEditing && (
                            <div className="mt-4 flex justify-end">
                              <Button 
                                variant="destructive" 
                                size="sm"
                                onClick={() => removeEducation(edu.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Work Experience */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <Briefcase className="h-5 w-5" />
                        <span>工作经历</span>
                      </CardTitle>
                      {isEditing && (
                        <Button onClick={addWorkExperience} size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          添加
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {workExperience.map((work) => (
                        <div key={work.id} className="p-4 border rounded-lg">
                          <div className="grid md:grid-cols-2 gap-4 mb-4">
                            <div className="space-y-2">
                              <Label>公司名称</Label>
                              <Input
                                value={work.company}
                                onChange={(e) => updateWorkExperience(work.id, 'company', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>职位</Label>
                              <Input
                                value={work.position}
                                onChange={(e) => updateWorkExperience(work.id, 'position', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>开始时间</Label>
                              <Input
                                type="month"
                                value={work.start_date}
                                onChange={(e) => updateWorkExperience(work.id, 'start_date', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>结束时间</Label>
                              <Input
                                type="month"
                                value={work.end_date}
                                onChange={(e) => updateWorkExperience(work.id, 'end_date', e.target.value)}
                                disabled={!isEditing}
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label>工作职责</Label>
                            <div className="space-y-2">
                              {work.responsibilities.map((resp, index) => (
                                <div key={index} className="flex space-x-2">
                                  <Input
                                    value={resp}
                                    onChange={(e) => {
                                      const newResponsibilities = [...work.responsibilities];
                                      newResponsibilities[index] = e.target.value;
                                      updateWorkExperience(work.id, 'responsibilities', newResponsibilities);
                                    }}
                                    disabled={!isEditing}
                                    placeholder="描述您的工作职责"
                                  />
                                  {isEditing && (
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      onClick={() => {
                                        const newResponsibilities = work.responsibilities.filter((_, i) => i !== index);
                                        updateWorkExperience(work.id, 'responsibilities', newResponsibilities);
                                      }}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              ))}
                              {isEditing && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const newResponsibilities = [...work.responsibilities, ""];
                                    updateWorkExperience(work.id, 'responsibilities', newResponsibilities);
                                  }}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  添加职责
                                </Button>
                              )}
                            </div>
                          </div>
                          {isEditing && (
                            <div className="mt-4 flex justify-end">
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeWorkExperience(work.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Skills */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Code className="h-5 w-5" />
                      <span>技能专长</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label>编程语言</Label>
                        <div className="flex flex-wrap gap-2">
                          {skills.programming_languages.map((skill, index) => (
                            <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>框架技术</Label>
                        <div className="flex flex-wrap gap-2">
                          {skills.frameworks.map((skill, index) => (
                            <span key={index} className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>数据库</Label>
                        <div className="flex flex-wrap gap-2">
                          {skills.databases.map((skill, index) => (
                            <span key={index} className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>开发工具</Label>
                        <div className="flex flex-wrap gap-2">
                          {skills.tools.map((skill, index) => (
                            <span key={index} className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
      </PageTransition>
    </ProtectedRoute>
  );
}
