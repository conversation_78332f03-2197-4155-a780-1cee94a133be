const packager = require('electron-packager');
const path = require('path');

console.log('🚀 开始简单打包 JobPlus 应用...\n');

async function packageApp() {
  try {
    console.log('📦 正在打包应用...');
    
    const appPaths = await packager({
      dir: '.',
      name: 'JobPlus AI面试助手',
      platform: 'win32',
      arch: 'x64',
      out: './dist-package',
      overwrite: true,
      asar: true,

      ignore: [
        /src/,
        /\.git/,
        /\.md$/,
        /\.log$/,
        /dist/,
        /dist-simple/,
        /build/,
        /node_modules\/(?!next)/
      ],
      appBundleId: 'com.jobplus.app',
      appVersion: '1.0.0',
      buildVersion: '1.0.0',
      appCopyright: 'Copyright © 2024 JobPlus Team',
      win32metadata: {
        CompanyName: 'JobPlus Team',
        FileDescription: 'JobPlus AI面试助手',
        OriginalFilename: 'JobPlus AI面试助手.exe',
        ProductName: 'JobPlus AI面试助手',
        InternalName: 'JobPlus'
      }
    });

    console.log('✅ 打包成功！');
    console.log('📁 应用位置:', appPaths[0]);
    console.log('🎉 JobPlus 应用已打包完成！');
    console.log('\n📋 使用说明:');
    console.log('1. 进入打包目录:', appPaths[0]);
    console.log('2. 运行 "JobPlus AI面试助手.exe"');
    console.log('3. 享受 JobPlus 桌面应用！');
    
    return appPaths;
  } catch (error) {
    console.error('❌ 打包失败:', error);
    process.exit(1);
  }
}

packageApp();
