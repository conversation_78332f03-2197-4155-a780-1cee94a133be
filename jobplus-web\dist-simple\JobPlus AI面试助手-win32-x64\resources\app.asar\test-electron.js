const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Testing Electron integration...');

// Check if development server is running
const checkServer = async () => {
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Development server is running');
      return true;
    }
  } catch (error) {
    console.log('❌ Development server is not running');
    console.log('Please start it with: npm run dev');
    return false;
  }
};

// Start Electron
const startElectron = () => {
  console.log('🖥️  Starting Electron...');
  
  const electronProcess = spawn('npx', ['electron', '.'], {
    stdio: 'inherit',
    cwd: __dirname
  });
  
  electronProcess.on('close', (code) => {
    console.log(`Electron process exited with code ${code}`);
  });
  
  electronProcess.on('error', (error) => {
    console.error('Failed to start Electron:', error);
  });
  
  return electronProcess;
};

// Main test function
const main = async () => {
  const serverRunning = await checkServer();
  
  if (serverRunning) {
    console.log('🎉 Starting Electron application...');
    startElectron();
  } else {
    console.log('Please start the development server first and try again.');
  }
};

main().catch(console.error);
