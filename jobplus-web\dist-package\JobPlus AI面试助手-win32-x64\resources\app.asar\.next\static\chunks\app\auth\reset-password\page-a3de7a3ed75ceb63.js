(global.webpackChunk_N_E=global.webpackChunk_N_E||[]).push([[89],{5311:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(5155),l=a(2115),r=a(5695),c=a(285),n=a(2523),i=a(5057),d=a(6695),h=a(646),x=a(9376),o=a(8749),u=a(2657),m=a(7292);function j(){let[e,s]=(0,l.useState)(""),[a,j]=(0,l.useState)(""),[p,N]=(0,l.useState)(!1),[b,f]=(0,l.useState)(!1),[g,v]=(0,l.useState)(!1),[w,y]=(0,l.useState)(""),[S,k]=(0,l.useState)(!1),A=(0,r.useRouter)();(0,r.useSearchParams)(),(0,l.useEffect)(()=>{(async()=>{let{data:e}=await m.j.auth.getSession();e.session||y("无效的重置链接，请重新申请密码重置")})()},[]);let C=async s=>{if(s.preventDefault(),y(""),e!==a?(y("两次输入的密码不一致"),!1):!(e.length<6)||(y("密码长度至少为6位"),!1)){v(!0);try{let{error:s}=await m.j.auth.updateUser({password:e});s?y(s.message):(k(!0),setTimeout(()=>{A.push("/auth/login")},2e3))}catch(e){y("密码重置失败，请稍后重试")}finally{v(!1)}}};return S?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsx)(d.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)(d.aR,{className:"text-center",children:[(0,t.jsx)(h.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,t.jsx)(d.ZB,{className:"text-2xl",children:"密码重置成功！"}),(0,t.jsx)(d.BT,{children:"您的密码已成功重置，即将跳转到登录页面..."})]})})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,t.jsxs)(d.aR,{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"JobPlus"})]}),(0,t.jsx)(d.ZB,{className:"text-2xl",children:"设置新密码"}),(0,t.jsx)(d.BT,{children:"请输入您的新密码"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.J,{htmlFor:"password",children:"新密码"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{id:"password",type:p?"text":"password",placeholder:"请输入新密码（至少6位）",value:e,onChange:e=>s(e.target.value),required:!0}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>N(!p),children:p?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.J,{htmlFor:"confirmPassword",children:"确认新密码"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{id:"confirmPassword",type:b?"text":"password",placeholder:"请再次输入新密码",value:a,onChange:e=>j(e.target.value),required:!0}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>f(!b),children:b?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})]}),w&&(0,t.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded-md",children:w}),(0,t.jsx)(c.$,{type:"submit",className:"w-full",disabled:g,children:g?"重置中...":"重置密码"})]})})]})})}function p(){return(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)("div",{children:"Loading..."}),children:(0,t.jsx)(j,{})})}},9278:(e,s,a)=>{Promise.resolve().then(a.bind(a,5311))}},e=>{e.O(0,[598,196,441,964,358],()=>e(e.s=9278)),_N_E=e.O()}]);