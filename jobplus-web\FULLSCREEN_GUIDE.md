# JobPlus 全屏模式使用指南

## 🎯 概述

JobPlus 桌面应用现在支持全屏模式，提供更沉浸式的面试体验。应用默认隐藏菜单栏，提供更简洁的界面。

## ✨ 功能特性

### 🖥️ 无菜单栏设计
- **隐藏菜单栏**: 应用启动时自动隐藏系统菜单栏
- **简洁界面**: 提供更大的可用屏幕空间
- **专注体验**: 减少界面干扰，专注于面试内容

### 🔄 全屏模式
- **一键切换**: 支持按钮和快捷键切换全屏
- **智能检测**: 自动检测当前全屏状态
- **状态同步**: 界面按钮与实际状态保持同步

## 🎮 操作方法

### 方法一：使用界面按钮
1. 在应用右上角找到全屏控制按钮
2. 点击 **最大化图标** 进入全屏模式
3. 点击 **最小化图标** 退出全屏模式

### 方法二：使用快捷键
- **F11**: 切换全屏模式（进入/退出）
- **ESC**: 退出全屏模式（仅在全屏时有效）

## 📍 全屏控制按钮位置

全屏控制按钮位于以下页面的右上角导航栏：
- ✅ **控制台页面** (Dashboard)
- 🔄 **其他页面** (可根据需要添加)

## 🛠️ 技术实现

### 核心组件
- **FullscreenControl**: 全屏控制按钮组件
- **FullscreenToggle**: 带文字说明的全屏切换组件

### API 接口
```typescript
// 切换全屏状态
await electronAPI.toggleFullscreen(): Promise<boolean>

// 检查当前全屏状态
await electronAPI.isFullscreen(): Promise<boolean>
```

### 快捷键处理
- 在 Electron 主进程中监听键盘事件
- 支持 F11 和 ESC 键的全屏控制
- 自动处理状态同步

## 🎨 用户体验

### 进入全屏模式时
- 隐藏操作系统任务栏
- 隐藏窗口标题栏
- 应用占据整个屏幕
- 按钮图标变为"最小化"

### 退出全屏模式时
- 恢复窗口模式
- 显示窗口标题栏
- 按钮图标变为"最大化"

## 🔧 开发者信息

### 添加全屏控制到新页面

1. **导入组件**:
```tsx
import { FullscreenControl } from '@/components/ui/fullscreen-control';
```

2. **添加到导航栏**:
```tsx
<nav className="flex items-center space-x-4">
  {/* 其他导航项 */}
  <FullscreenControl />
</nav>
```

### 自定义样式
全屏控制按钮支持标准的 Button 组件属性：
- `variant`: 按钮样式变体
- `size`: 按钮大小
- `className`: 自定义 CSS 类

## 🚀 最佳实践

### 面试场景
- **进入面试前**: 建议切换到全屏模式以减少干扰
- **录音时**: 全屏模式提供更专业的面试环境
- **回顾记录**: 可在窗口模式下查看历史记录

### 性能优化
- 全屏模式下应用性能更佳
- 减少系统资源占用
- 提供更流畅的用户体验

## 🐛 故障排除

### 常见问题

**Q: 全屏按钮不显示？**
A: 确保在 Electron 环境中运行，Web 版本不显示此按钮。

**Q: F11 快捷键不工作？**
A: 确保应用窗口处于焦点状态，然后按 F11。

**Q: 无法退出全屏？**
A: 尝试按 ESC 键或点击全屏控制按钮。

### 重启应用
如果遇到问题，可以重启 Electron 应用：
```bash
# 关闭所有 Electron 进程
taskkill /f /im electron.exe

# 重新启动应用
npx electron .
```

## 📝 更新日志

### v1.0.0
- ✅ 添加全屏模式支持
- ✅ 隐藏菜单栏
- ✅ 添加 F11 和 ESC 快捷键
- ✅ 创建全屏控制组件
- ✅ 集成到控制台页面

---

**提示**: 全屏模式特别适合面试场景，能够提供更专业、更专注的用户体验。建议在正式面试时使用全屏模式。
