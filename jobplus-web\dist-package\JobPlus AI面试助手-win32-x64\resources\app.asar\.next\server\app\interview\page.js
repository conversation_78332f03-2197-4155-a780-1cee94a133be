(()=>{var a={};a.id=66,a.ids=[66],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1804:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\jobplus\\\\1-前端服务\\\\ai-jobplus-web6\\\\jobplus-web\\\\src\\\\app\\\\interview\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\interview\\page.tsx","default")},2942:(a,b,c)=>{Promise.resolve().then(c.bind(c,1804))},2943:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15807:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58869:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-square",[["path",{d:"M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z",key:"18887p"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66458:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});class d{saveSession(a){try{let b=this.getAllSessions(),c=b.findIndex(b=>b.id===a.id);return c>=0?b[c]=a:b.push(a),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(b)),this.updateStats(),!0}catch(a){return console.error("Failed to save interview session:",a),!1}}getAllSessions(a){try{let b=localStorage.getItem(this.STORAGE_KEY);if(!b)return[];let c=JSON.parse(b).map(a=>({...a,startTime:new Date(a.startTime),endTime:a.endTime?new Date(a.endTime):void 0,messages:a.messages.map(a=>({...a,timestamp:new Date(a.timestamp)}))}));if(a)return c.filter(b=>b.userId===a);return c}catch(a){return console.error("Failed to load interview sessions:",a),[]}}getSessionById(a){return this.getAllSessions().find(b=>b.id===a)||null}deleteSession(a){try{let b=this.getAllSessions().filter(b=>b.id!==a);return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(b)),this.updateStats(),!0}catch(a){return console.error("Failed to delete interview session:",a),!1}}updateSessionNotes(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].userNotes=b,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),!0;return!1}catch(a){return console.error("Failed to update session notes:",a),!1}}updateSessionRating(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].rating=Math.max(1,Math.min(5,b)),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),this.updateStats(),!0;return!1}catch(a){return console.error("Failed to update session rating:",a),!1}}updateSessionTags(a,b){try{let c=this.getAllSessions(),d=c.findIndex(b=>b.id===a);if(d>=0)return c[d].tags=b,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(c)),!0;return!1}catch(a){return console.error("Failed to update session tags:",a),!1}}getFilteredSessions(a){let b=this.getAllSessions(a.userId);return a.company&&(b=b.filter(b=>b.company.toLowerCase().includes(a.company.toLowerCase()))),a.position&&(b=b.filter(b=>b.position.toLowerCase().includes(a.position.toLowerCase()))),a.interviewType&&(b=b.filter(b=>b.interviewType===a.interviewType)),a.status&&(b=b.filter(b=>b.status===a.status)),a.dateFrom&&(b=b.filter(b=>b.startTime>=a.dateFrom)),a.dateTo&&(b=b.filter(b=>b.startTime<=a.dateTo)),a.minRating&&(b=b.filter(b=>(b.rating||0)>=a.minRating)),a.tags&&a.tags.length>0&&(b=b.filter(b=>b.tags&&b.tags.some(b=>a.tags.includes(b)))),b.sort((a,b)=>b.startTime.getTime()-a.startTime.getTime())}getStats(a){try{let b=this.getAllSessions(a),c=b.filter(a=>"completed"===a.status);if(0===c.length)return{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]};let d=c.reduce((a,b)=>a+b.duration,0),e=c.filter(a=>a.summary?.overallScore).reduce((a,b)=>a+(b.summary?.overallScore||0),0)/c.filter(a=>a.summary?.overallScore).length||0,f=new Map;c.forEach(a=>{f.set(a.company,(f.get(a.company)||0)+1)});let g=Array.from(f.entries()).map(([a,b])=>({company:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,5),h=new Map;c.forEach(a=>{h.set(a.position,(h.get(a.position)||0)+1)});let i=Array.from(h.entries()).map(([a,b])=>({position:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,5),j=this.calculateMonthlyProgress(c),k=this.calculateSkillProgress(c);return{totalSessions:b.length,totalDuration:d,averageScore:Math.round(e),completionRate:Math.round(c.length/b.length*100),topCompanies:g,topPositions:i,monthlyProgress:j,skillProgress:k}}catch(a){return console.error("Failed to calculate stats:",a),{totalSessions:0,totalDuration:0,averageScore:0,completionRate:0,topCompanies:[],topPositions:[],monthlyProgress:[],skillProgress:[]}}}calculateMonthlyProgress(a){let b=new Map;return a.forEach(a=>{let c=a.startTime.toISOString().substring(0,7),d=b.get(c)||{sessions:0,totalScore:0,count:0};d.sessions++,a.summary?.overallScore&&(d.totalScore+=a.summary.overallScore,d.count++),b.set(c,d)}),Array.from(b.entries()).map(([a,b])=>({month:a,sessions:b.sessions,avgScore:b.count>0?Math.round(b.totalScore/b.count):0})).sort((a,b)=>a.month.localeCompare(b.month)).slice(-6)}calculateSkillProgress(a){return["技术能力","沟通表达","逻辑思维","团队协作","学习能力"].map(b=>{let c=a.slice(-5).filter(a=>a.summary?.overallScore).map(a=>a.summary.overallScore),d=c.length>0?Math.round(c.reduce((a,b)=>a+b,0)/c.length):0,e="stable";if(c.length>=3){let a=c.slice(0,Math.floor(c.length/2)),b=c.slice(Math.floor(c.length/2)),d=a.reduce((a,b)=>a+b,0)/a.length,f=b.reduce((a,b)=>a+b,0)/b.length;f>d+5?e="up":f<d-5&&(e="down")}return{skill:b,score:d,trend:e}})}updateStats(){}exportSessions(a){return JSON.stringify(this.getAllSessions(a),null,2)}importSessions(a){try{let b=JSON.parse(a),c=this.getAllSessions(),d=new Map;c.forEach(a=>{d.set(a.id,a)}),b.forEach(a=>{d.set(a.id,a)});let e=Array.from(d.values());return localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e)),this.updateStats(),!0}catch(a){return console.error("Failed to import sessions:",a),!1}}clearAllData(){try{return localStorage.removeItem(this.STORAGE_KEY),localStorage.removeItem(this.STATS_KEY),!0}catch(a){return console.error("Failed to clear data:",a),!1}}constructor(){this.STORAGE_KEY="jobplus_interview_sessions",this.STATS_KEY="jobplus_interview_stats"}}},71398:(a,b,c)=>{Promise.resolve().then(c.bind(c,90228))},83753:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88800:(a,b,c)=>{"use strict";c.d(b,{J:()=>l});var d=c(60687),e=c(43210);c(51215);var f=c(81391),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),g=e.forwardRef((a,e)=>{let{asChild:f,...g}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f?c:b,{...g,ref:e})});return g.displayName=`Primitive.${b}`,{...a,[b]:g}},{}),h=e.forwardRef((a,b)=>(0,d.jsx)(g.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));h.displayName="Label";var i=c(24224),j=c(4780);let k=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(h,{ref:c,className:(0,j.cn)(k(),a),...b}));l.displayName=h.displayName},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},90228:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>L});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(63213),i=c(20769),j=c(29523),k=c(89667),l=c(88800),m=c(44493);class n{constructor(a={sampleRate:44100,channels:1,bitDepth:16,echoCancellation:!0,noiseSuppression:!0}){this.config=a,this.mediaRecorder=null,this.audioContext=null,this.analyser=null,this.microphone=null,this.dataArray=null,this.stream=null,this.isRecording=!1,this.audioChunks=[]}async initialize(){try{return this.stream=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:this.config.sampleRate,channelCount:this.config.channels,echoCancellation:this.config.echoCancellation,noiseSuppression:this.config.noiseSuppression}}),this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.analyser=this.audioContext.createAnalyser(),this.microphone=this.audioContext.createMediaStreamSource(this.stream),this.analyser.fftSize=256,this.dataArray=new Uint8Array(this.analyser.frequencyBinCount),this.microphone.connect(this.analyser),this.mediaRecorder=new MediaRecorder(this.stream,{mimeType:this.getSupportedMimeType()}),this.setupMediaRecorderEvents(),!0}catch(a){return console.error("Failed to initialize audio processor:",a),!1}}getSupportedMimeType(){for(let a of["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/wav"])if(MediaRecorder.isTypeSupported(a))return a;return"audio/webm"}setupMediaRecorderEvents(){this.mediaRecorder&&(this.mediaRecorder.ondataavailable=a=>{a.data.size>0&&this.audioChunks.push(a.data)},this.mediaRecorder.onstop=()=>{let a=new Blob(this.audioChunks,{type:this.mediaRecorder?.mimeType||"audio/webm"});this.processAudioBlob(a),this.audioChunks=[]})}startRecording(){if(!this.mediaRecorder||this.isRecording)return!1;try{return this.audioChunks=[],this.mediaRecorder.start(1e3),this.isRecording=!0,this.startAudioAnalysis(),!0}catch(a){return console.error("Failed to start recording:",a),!1}}stopRecording(){if(!this.mediaRecorder||!this.isRecording)return!1;try{return this.mediaRecorder.stop(),this.isRecording=!1,this.stopAudioAnalysis(),!0}catch(a){return console.error("Failed to stop recording:",a),!1}}startAudioAnalysis(){if(!this.analyser||!this.dataArray)return;let a=()=>{if(!this.isRecording)return;this.analyser.getByteFrequencyData(this.dataArray);let b=0;for(let a=0;a<this.dataArray.length;a++)b+=this.dataArray[a]*this.dataArray[a];let c=Math.sqrt(b/this.dataArray.length)/255,d=c>.1,e=0,f=0;for(let a=0;a<this.dataArray.length;a++)this.dataArray[a]>f&&(f=this.dataArray[a],e=a);let g={volume:100*c,frequency:e*this.config.sampleRate/(2*this.analyser.fftSize),speechDetected:d,silenceDuration:d?0:Date.now()};this.onDataCallback&&this.onDataCallback(g),requestAnimationFrame(a)};a()}stopAudioAnalysis(){}async processAudioBlob(a){try{let b=await this.mockSpeechRecognition(a);this.onSpeechCallback&&b&&this.onSpeechCallback(b)}catch(a){console.error("Failed to process audio blob:",a)}}async mockSpeechRecognition(a){if(await new Promise(a=>setTimeout(a,1e3+2e3*Math.random())),a.size/1e3<1)return null;let b=["我有三年的前端开发经验，主要使用React和Vue.js框架。","我最大的优势是学习能力强，能够快速适应新技术和新环境。","我希望在贵公司能够承担更多的技术挑战，提升自己的技术水平。","我对这个职位很感兴趣，因为它符合我的职业规划和发展方向。","我在之前的项目中负责了整个前端架构的设计和实现。","我认为团队协作非常重要，我善于与不同背景的同事沟通合作。","我会持续关注行业动态，学习新的技术和最佳实践。","我希望能够在技术和管理方面都有所发展。"];return{text:b[Math.floor(Math.random()*b.length)],confidence:.8+.2*Math.random(),isFinal:!0,timestamp:new Date}}setOnDataCallback(a){this.onDataCallback=a}setOnSpeechCallback(a){this.onSpeechCallback=a}getRecordingState(){return this.isRecording}async cleanup(){this.isRecording&&this.stopRecording(),this.stream&&(this.stream.getTracks().forEach(a=>a.stop()),this.stream=null),this.audioContext&&(await this.audioContext.close(),this.audioContext=null),this.mediaRecorder=null,this.analyser=null,this.microphone=null,this.dataArray=null}static isSupported(){return"undefined"!=typeof navigator&&navigator.mediaDevices&&navigator.mediaDevices.getUserMedia,!1}static async requestPermissions(){try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(a=>a.stop()),!0}catch(a){return console.error("Microphone permission denied:",a),!1}}}class o{constructor(a){this.conversationHistory=[],this.context=a}async analyzeResponse(a,b){this.conversationHistory.push({type:"user",content:a,timestamp:new Date}),await new Promise(a=>setTimeout(a,1e3+1500*Math.random()));let c=this.analyzeResponseQuality(a);return this.generateFeedback(a,c,b)}async analyzeQuestion(a){await new Promise(a=>setTimeout(a,500));let b=this.classifyQuestion(a);return{questionType:b,difficulty:this.assessQuestionDifficulty(a),keywords:this.extractKeywords(a),expectedAnswerStructure:this.getAnswerStructure(b),tips:this.getQuestionSpecificTips(b,a)}}async generateProactiveSuggestion(){let a=this.conversationHistory.slice(-3);if(a.length<2)return null;let b=this.analyzeResponsePatterns(a);return b.needsImprovement?{type:"suggestion",content:b.suggestion,confidence:.8,category:b.category,suggestions:b.actionItems}:null}analyzeResponseQuality(a){let b=a.split(" ").length;return{length:b<20?"too_short":b>200?"too_long":"appropriate",structure:this.assessStructure(a),specificity:this.assessSpecificity(a),confidence:this.assessConfidence(a)}}assessStructure(a){let b=/^(我认为|我觉得|在我看来|根据我的经验)/.test(a),c=+!!b+ +!!/(例如|比如|举个例子|具体来说)/.test(a)+ +!!/(总的来说|综上所述|因此|所以)/.test(a);return c>=2?"excellent":1===c?"good":"poor"}assessSpecificity(a){let b=[/\d+年/,/\d+个月/,/\d+%/,/具体来说/,/项目中/,/公司/,/技术栈/].reduce((b,c)=>b+ +!!c.test(a),0);return b>=3?"very_specific":b>=1?"specific":"vague"}assessConfidence(a){let b=[/我确信/,/我相信/,/我擅长/,/我能够/].some(b=>b.test(a)),c=[/我觉得可能/,/也许/,/我不太确定/,/可能/].some(b=>b.test(a));return b&&!c?"high":c&&!b?"low":"medium"}generateFeedback(a,b,c){let d={poor:"建议使用更清晰的结构来组织回答，比如：观点-例子-总结。",good:"回答结构不错，可以进一步优化逻辑顺序。",excellent:"回答结构非常清晰，逻辑性强！"},e="",f=[],g="suggestion";return"too_short"===b.length?(e="您的回答比较简短，建议提供更多细节和具体例子来支撑您的观点。",f.push("添加具体例子","提供更多细节","说明影响和结果")):"poor"===b.structure?(e=d.poor,f.push("使用STAR方法","先说观点再举例","最后总结要点")):"vague"===b.specificity?(e="建议提供更具体的例子、数据或项目经验来支撑您的回答。",f.push("提供具体数据","举实际项目例子","说明技术细节")):(e="回答很不错！"+d[b.structure],g="encouragement",f.push("保持这种回答风格","继续展现专业能力","适当补充相关经验")),"technical"===this.context.interviewType?f.push("展示技术深度","说明解决方案","提及最佳实践"):"behavioral"===this.context.interviewType&&f.push("使用STAR方法","强调团队合作","展示学习能力"),{type:g,content:e,suggestions:f.slice(0,4),confidence:.85,category:this.determineCategory(c||a)}}classifyQuestion(a){return["技术","代码","算法","架构","框架","数据库","性能","优化"].some(b=>a.includes(b))?"technical":["团队","冲突","挑战","失败","成功","领导","合作","沟通"].some(b=>a.includes(b))?"behavioral":["如果","假设","遇到","处理","解决","应对"].some(b=>a.includes(b))?"situational":"general"}assessQuestionDifficulty(a){return["架构","设计模式","性能优化","分布式","高并发"].some(b=>a.includes(b))?"hard":["介绍","了解","基础","简单"].some(b=>a.includes(b))?"easy":"medium"}extractKeywords(a){let b=["的","是","在","有","和","与","或","但","如果","那么","什么","怎么","为什么"];return a.split(/\s+|[，。！？；：]/).filter(a=>a.length>1&&!b.includes(a)).slice(0,5)}getAnswerStructure(a){return({technical:["理解问题","说明方案","举例说明","总结优势"],behavioral:["情况描述","任务说明","行动过程","结果总结"],situational:["分析情况","制定策略","执行步骤","预期结果"],general:["观点表达","支撑论据","具体例子","简要总结"]})[a]}getQuestionSpecificTips(a,b){let c={technical:["展示技术深度和广度","提供具体的代码示例或架构图","说明技术选择的原因","讨论性能和可维护性"],behavioral:["使用STAR方法组织回答","强调个人贡献和成长","展示团队合作能力","体现解决问题的思路"],situational:["展示分析问题的能力","提出多种解决方案","考虑风险和应对措施","体现决策思维过程"],general:["保持回答的逻辑性","提供具体的例子","展现积极的态度","与职位要求相关联"]};return c[a]||c.general}analyzeResponsePatterns(a){let b=a.map(a=>a.content).join(" ");return["我觉得","我认为","可能","应该"].reduce((a,c)=>a+(b.split(c).length-1),0)>5?{needsImprovement:!0,suggestion:"注意避免重复使用相同的表达方式，尝试使用更多样化的语言来表达观点。",category:"communication",actionItems:["使用同义词替换","变换句式结构","增加表达多样性"]}:a.reduce((a,b)=>a+ +("vague"!==this.assessSpecificity(b.content)),0)<a.length/2?{needsImprovement:!0,suggestion:"建议在回答中增加更多具体的例子、数据和项目经验，让回答更有说服力。",category:"general",actionItems:["提供具体数据","举实际例子","说明具体成果"]}:{needsImprovement:!1,suggestion:"",category:"general",actionItems:[]}}determineCategory(a){return/技术|代码|架构|算法/.test(a)?"technical":/团队|沟通|合作|领导/.test(a)?"communication":/经验|挑战|成长|学习/.test(a)?"behavioral":"general"}getConversationSummary(){let a=this.conversationHistory.filter(a=>"user"===a.type),b=a.length,c=a.reduce((a,b)=>a+b.content.split(" ").length,0)/b,d=a.map(a=>this.analyzeResponseQuality(a.content)),e=[],f=[],g=d.map(a=>a.structure),h=d.map(a=>a.specificity),i=d.map(a=>a.confidence);g.filter(a=>"excellent"===a).length>b/2&&e.push("回答结构清晰"),h.filter(a=>"very_specific"===a).length>b/2&&e.push("提供具体例子"),i.filter(a=>"high"===a).length>b/2&&e.push("表现自信"),g.filter(a=>"poor"===a).length>b/3&&f.push("改善回答结构"),h.filter(a=>"vague"===a).length>b/3&&f.push("增加具体细节");let j={poor:0,good:.7,excellent:1},k={vague:0,specific:.7,very_specific:1},l={low:0,medium:.6,high:1},m=Math.round(d.reduce((a,b)=>a+(j[b.structure]+k[b.specificity]+l[b.confidence])/3,0)/b*100);return{totalResponses:b,averageResponseLength:Math.round(c),strongPoints:e,improvementAreas:f,overallScore:m}}}var p=c(66458),q=c(78200),r=c(40083),s=c(2943),t=c(5336),u=c(93613),v=c(62688);let w=(0,v.A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]);var x=c(48730);let y=(0,v.A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),z=(0,v.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var A=c(58869),B=c(83753),C=c(58887);let D=(0,v.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),E=(0,v.A)("mic-off",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),F=(0,v.A)("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]]),G=(0,v.A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);var H=c(15807),I=c(41008),J=c(62694),K=c(94934);function L(){let{user:a}=(0,h.A)(),b=(0,J.EM)(),[c,f]=(0,e.useState)(!1),[v,L]=(0,e.useState)(!1),[M,N]=(0,e.useState)(0),[O,P]=(0,e.useState)(null),[Q,R]=(0,e.useState)([]),[S,T]=(0,e.useState)(""),[U,V]=(0,e.useState)(!1),[W,X]=(0,e.useState)(0),[Y,Z]=(0,e.useState)(!0),[$,_]=(0,e.useState)(!1),[aa,ab]=(0,e.useState)(null),[ac,ad]=(0,e.useState)({company:"",position:"",interviewType:"technical",difficulty:"intermediate"}),ae=(0,e.useRef)(null),af=(0,e.useRef)(null),ag=(0,e.useRef)(null);(0,e.useRef)(null);let ah=(0,e.useRef)(new p.E),ai=a=>{let b=Math.floor(a/60);return`${b.toString().padStart(2,"0")}:${(a%60).toString().padStart(2,"0")}`},aj=async()=>{if(!ac.company||!ac.position)return void b.warning("请填写完整信息","请填写公司名称和职位");if(!Y)return void b.error("浏览器不支持","您的浏览器不支持音频录制功能");if(!$){if(!await n.requestPermissions())return void b.error("权限不足","需要麦克风权限才能使用语音功能");_(!0),b.success("权限获取成功","麦克风权限已获取")}if(ae.current=new n,!await ae.current.initialize())return void b.error("初始化失败","音频初始化失败，请检查麦克风设置");ae.current.setOnDataCallback(a=>{ab(a),N(a.volume)}),ae.current.setOnSpeechCallback(async a=>{if(a.isFinal&&a.text.trim()){let b={id:Date.now().toString(),type:"user",content:a.text,timestamp:a.timestamp};if(R(a=>[...a,b]),af.current){V(!0);try{let b=await af.current.analyzeResponse(a.text),c={id:(Date.now()+1).toString(),type:"ai",content:b.content,timestamp:new Date,suggestions:b.suggestions};R(a=>[...a,c])}catch(a){console.error("AI analysis failed:",a)}finally{V(!1)}}}}),af.current=new o({company:ac.company,position:ac.position,interviewType:ac.interviewType,difficulty:ac.difficulty}),P({id:Date.now().toString(),company:ac.company,position:ac.position,interviewType:ac.interviewType,difficulty:ac.difficulty,startTime:new Date,status:"active"}),X(0),R([{id:Date.now().toString(),type:"ai",content:`欢迎来到${ac.company}的${ac.position}面试！我是您的AI面试助手，将为您提供实时建议和支持。请开始您的面试，我会在适当的时候为您提供帮助。`,timestamp:new Date}])},ak=async()=>{if(O&&a){P({...O,status:"completed"}),am();let b=`面试结束。总时长：${ai(W)}。感谢您使用JobPlus面试助手！`,c=null;if(af.current)try{let a=af.current.getConversationSummary();c={...a,keyInsights:a.strongPoints.length>0?a.strongPoints:["面试表现良好"],recommendedActions:a.improvementAreas.length>0?a.improvementAreas:["继续保持"]},b+=`

📊 面试总结：
• 回答次数：${c.totalResponses}
• 平均回答长度：${c.averageResponseLength}字
• 综合评分：${c.overallScore}/100`,c.strongPoints.length>0&&(b+=`
• 优势：${c.strongPoints.join("、")}`),c.improvementAreas.length>0&&(b+=`
• 改进建议：${c.improvementAreas.join("、")}`)}catch(a){console.error("Failed to generate summary:",a)}let d=[...Q,{id:Date.now().toString(),type:"system",content:b,timestamp:new Date}];R(d);try{let b={id:O.id,userId:a.id,company:O.company,position:O.position,interviewType:O.interviewType,difficulty:O.difficulty,startTime:O.startTime,endTime:new Date,duration:W,status:"completed",messages:d.map(a=>({id:a.id,type:a.type,content:a.content,timestamp:a.timestamp,isQuestion:a.isQuestion,suggestions:a.suggestions})),summary:c||void 0};ah.current.saveSession(b)?console.log("Interview session saved successfully"):console.error("Failed to save interview session")}catch(a){console.error("Error saving interview session:",a)}ae.current&&(await ae.current.cleanup(),ae.current=null)}},al=async()=>{if(!ae.current)return void b.error("系统错误","音频处理器未初始化");ae.current.startRecording()?(f(!0),L(!0),b.success("录音开始","正在监听您的语音...")):b.error("录音失败","无法开始录音，请检查麦克风权限")},am=()=>{ae.current&&c&&(ae.current.stopRecording(),f(!1),L(!1),N(0),ab(null))},an=async()=>{if(!S.trim())return;let a={id:Date.now().toString(),type:"user",content:S,timestamp:new Date};if(R(b=>[...b,a]),T(""),af.current){V(!0);try{let a=await af.current.analyzeResponse(S),b={id:(Date.now()+1).toString(),type:"ai",content:a.content,timestamp:new Date,suggestions:a.suggestions};R(a=>[...a,b])}catch(b){console.error("AI analysis failed:",b);let a={id:(Date.now()+1).toString(),type:"ai",content:"收到您的消息，我正在分析中。请继续您的面试。",timestamp:new Date};R(b=>[...b,a])}finally{V(!1)}}};return O?(0,d.jsx)(i.A,{children:(0,d.jsx)(I.Z_,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex flex-col",children:[(0,d.jsx)("header",{className:"bg-white border-b flex-shrink-0",children:(0,d.jsx)("div",{className:"container mx-auto px-4 py-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"h-6 w-6 text-blue-600"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"JobPlus"})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[O.company," - ",O.position]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(x.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:ai(W)})]}),(0,d.jsx)(g(),{href:"/history",children:(0,d.jsxs)(j.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(y,{className:"h-4 w-4 mr-2"}),"历史记录"]})}),(0,d.jsxs)(j.$,{onClick:ak,variant:"destructive",size:"sm",children:[(0,d.jsx)(z,{className:"h-4 w-4 mr-2"}),"结束面试"]})]})]})})}),(0,d.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[Q.map(a=>(0,d.jsx)("div",{className:`flex ${"user"===a.type?"justify-end":"justify-start"}`,children:(0,d.jsxs)("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${"user"===a.type?"bg-blue-600 text-white":"ai"===a.type?"bg-green-100 text-green-900":"bg-gray-100 text-gray-900"}`,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:["user"===a.type?(0,d.jsx)(A.A,{className:"h-4 w-4"}):"ai"===a.type?(0,d.jsx)(B.A,{className:"h-4 w-4"}):(0,d.jsx)(C.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-xs opacity-75",children:a.timestamp.toLocaleTimeString()})]}),(0,d.jsx)("p",{className:"text-sm",children:a.content}),a.suggestions&&(0,d.jsx)("div",{className:"mt-2 space-y-1",children:a.suggestions.map((a,b)=>(0,d.jsxs)("div",{className:"text-xs bg-white bg-opacity-20 px-2 py-1 rounded",children:["\uD83D\uDCA1 ",a]},b))})]})},a.id)),U&&(0,d.jsx)("div",{className:"flex justify-start",children:(0,d.jsx)("div",{className:"bg-gray-100 text-gray-900 px-4 py-2 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"text-sm",children:"AI正在分析..."})]})})}),(0,d.jsx)("div",{ref:ag})]}),(0,d.jsx)("div",{className:"border-t bg-white p-4",children:(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(k.p,{value:S,onChange:a=>T(a.target.value),placeholder:"输入您的问题或需要帮助的内容...",onKeyPress:a=>"Enter"===a.key&&an(),className:"flex-1"}),(0,d.jsx)(j.$,{onClick:an,disabled:!S.trim(),children:(0,d.jsx)(D,{className:"h-4 w-4"})})]})})]}),(0,d.jsxs)("div",{className:"w-80 border-l bg-white p-4 space-y-4",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900",children:"语音控制"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(j.$,{onClick:c?am:al,className:`w-full ${c?"bg-red-600 hover:bg-red-700":"bg-blue-600 hover:bg-blue-700"}`,size:"lg",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(E,{className:"h-5 w-5 mr-2"}),"停止录音"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(F,{className:"h-5 w-5 mr-2"}),"开始录音"]})}),v&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-600 flex items-center justify-between",children:[(0,d.jsx)("span",{children:"音频级别"}),aa?.speechDetected&&(0,d.jsxs)("span",{className:"text-xs text-green-600 flex items-center",children:[(0,d.jsx)(G,{className:"h-3 w-3 mr-1"}),"检测到语音"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:`h-2 rounded-full transition-all duration-100 ${aa?.speechDetected?"bg-green-600":"bg-blue-600"}`,style:{width:`${M}%`}})}),aa&&(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["音量: ",Math.round(M),"% | 频率: ",Math.round(aa.frequency),"Hz"]})]})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:[(0,d.jsxs)("h4",{className:"font-medium text-yellow-900 mb-2 flex items-center",children:[(0,d.jsx)(H.A,{className:"h-4 w-4 mr-2"}),"面试技巧"]}),(0,d.jsxs)("ul",{className:"text-xs text-yellow-800 space-y-1",children:[(0,d.jsx)("li",{children:"• 保持眼神交流"}),(0,d.jsx)("li",{children:"• 回答要具体有例子"}),(0,d.jsx)("li",{children:"• 展示学习能力"}),(0,d.jsx)("li",{children:"• 提问显示兴趣"})]})]}),(0,d.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"本次面试"}),(0,d.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,d.jsxs)("div",{children:["时长: ",ai(W)]}),(0,d.jsxs)("div",{children:["消息: ",Q.length]}),(0,d.jsxs)("div",{children:["状态: ","active"===O.status?"进行中":"已暂停"]})]})]})]})]})]})})}):(0,d.jsx)(i.A,{children:(0,d.jsx)(I.Z_,{children:(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,d.jsx)(I._A,{direction:"down",children:(0,d.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b shadow-sm",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"h-8 w-8 text-blue-600"}),(0,d.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"JobPlus"})]}),(0,d.jsxs)("nav",{className:"flex items-center space-x-4",children:[(0,d.jsx)(g(),{href:"/dashboard",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"控制台"}),(0,d.jsx)(g(),{href:"/resume",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"简历中心"}),(0,d.jsx)(g(),{href:"/interview",className:"text-blue-600 font-medium px-3 py-2 rounded-md bg-blue-50",children:"面试室"}),(0,d.jsx)(g(),{href:"/history",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100 transition-colors",children:"面试记录"}),(0,d.jsx)(j.$,{variant:"ghost",size:"icon",onClick:()=>K.f.signOut(),className:"hover:bg-red-50 hover:text-red-600",children:(0,d.jsx)(r.A,{className:"h-4 w-4"})})]})]})})}),(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,d.jsx)(I._A,{delay:200,children:(0,d.jsxs)("div",{className:"mb-8 text-center",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4",children:"AI面试助手"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"设置您的面试信息，AI助手将为您提供实时建议和指导"})]})}),(0,d.jsx)(I._A,{delay:400,children:(0,d.jsxs)(m.Zp,{className:"shadow-lg border-0 bg-white/80 backdrop-blur-sm",children:[(0,d.jsxs)(m.aR,{className:"text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-t-lg",children:[(0,d.jsxs)(m.ZB,{className:"text-2xl text-gray-800 flex items-center justify-center space-x-2",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-md",children:(0,d.jsx)(s.A,{className:"h-6 w-6 text-white"})}),(0,d.jsx)("span",{children:"开始面试"})]}),(0,d.jsx)(m.BT,{className:"text-gray-600",children:"设置您的面试信息，AI助手将为您提供实时建议"})]}),(0,d.jsxs)(m.Wu,{className:"space-y-6 p-8",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"company",className:"text-gray-700 font-medium",children:"公司名称"}),(0,d.jsx)(k.p,{id:"company",value:ac.company,onChange:a=>ad(b=>({...b,company:a.target.value})),placeholder:"请输入公司名称",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{htmlFor:"position",className:"text-gray-700 font-medium",children:"应聘职位"}),(0,d.jsx)(k.p,{id:"position",value:ac.position,onChange:a=>ad(b=>({...b,position:a.target.value})),placeholder:"请输入应聘职位",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400"})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{className:"text-gray-700 font-medium",children:"面试类型"}),(0,d.jsxs)("select",{className:"w-full p-3 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-20 transition-all",value:ac.interviewType,onChange:a=>ad(b=>({...b,interviewType:a.target.value})),children:[(0,d.jsx)("option",{value:"technical",children:"技术面试"}),(0,d.jsx)("option",{value:"behavioral",children:"行为面试"}),(0,d.jsx)("option",{value:"mixed",children:"综合面试"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(l.J,{className:"text-gray-700 font-medium",children:"难度级别"}),(0,d.jsxs)("select",{className:"w-full p-3 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-20 transition-all",value:ac.difficulty,onChange:a=>ad(b=>({...b,difficulty:a.target.value})),children:[(0,d.jsx)("option",{value:"beginner",children:"初级"}),(0,d.jsx)("option",{value:"intermediate",children:"中级"}),(0,d.jsx)("option",{value:"advanced",children:"高级"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200",children:[(0,d.jsxs)("h4",{className:"font-medium text-blue-900 mb-3 flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"AI助手功能"})]}),(0,d.jsxs)("ul",{className:"text-sm text-blue-800 space-y-2",children:[(0,d.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,d.jsx)("span",{children:"实时语音识别和转录"})]}),(0,d.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,d.jsx)("span",{children:"智能回答建议和优化"})]}),(0,d.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,d.jsx)("span",{children:"面试技巧和要点提醒"})]}),(0,d.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,d.jsx)("span",{children:"表现评估和改进建议"})]})]})]}),(0,d.jsx)("div",{className:`p-4 rounded-lg border ${Y&&$?"bg-green-50 border-green-200":"bg-yellow-50 border-yellow-200"}`,children:(0,d.jsx)("div",{className:"flex items-center space-x-3",children:Y&&$?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(t.A,{className:"h-4 w-4 text-white"})}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800",children:"音频功能已就绪"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(u.A,{className:"h-4 w-4 text-white"})}),(0,d.jsx)("span",{className:"text-sm font-medium text-yellow-800",children:Y?"需要麦克风权限":"浏览器不支持音频录制"})]})})}),(0,d.jsxs)(j.$,{onClick:aj,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg",size:"lg",children:[(0,d.jsx)(w,{className:"h-5 w-5 mr-2"}),"开始面试"]})]})]})})]})})]})})})}},94838:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["interview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1804)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\interview\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\jobplus\\1-前端服务\\ai-jobplus-web6\\jobplus-web\\src\\app\\interview\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/interview/page",pathname:"/interview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/interview/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,852,814,300,219],()=>b(b.s=94838));module.exports=c})();