const { build } = require('electron-builder');
const path = require('path');

console.log('🚀 开始构建 JobPlus EXE 安装包...\n');

const config = {
  appId: 'com.jobplus.app',
  productName: 'JobPlus AI面试助手',
  directories: {
    output: 'dist',
    buildResources: 'build'
  },
  files: [
    '.next/**/*',
    'public/**/*',
    'electron/**/*',
    'package.json',
    '!node_modules/**/*',
    '!src/**/*',
    '!*.md',
    '!*.log'
  ],
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64']
      }
    ],
    icon: 'build/icon.ico',
    requestedExecutionLevel: 'asInvoker',
    artifactName: '${productName}-${version}-Setup.${ext}'
  },
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    installerIcon: 'build/icon.ico',
    uninstallerIcon: 'build/icon.ico',
    installerHeaderIcon: 'build/icon.ico',
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'JobPlus AI面试助手',
    displayLanguageSelector: false,
    installerLanguages: ['zh_CN'],
    language: '2052'
  }
};

async function buildApp() {
  try {
    console.log('📦 开始打包应用...');
    
    const result = await build({
      targets: require('electron-builder').Platform.WINDOWS.createTarget(),
      config: config,
      publish: 'never'
    });
    
    console.log('✅ 构建成功！');
    console.log('📁 安装包位置: dist/');
    console.log('🎉 JobPlus EXE 安装包已生成完成！');
    
    return result;
  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

buildApp();
